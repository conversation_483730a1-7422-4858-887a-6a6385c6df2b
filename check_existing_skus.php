<?php
// <PERSON><PERSON><PERSON> to check existing SKUs and help resolve duplication issues
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html><head><title>Check Existing SKUs</title>";
echo "<style>body{font-family:Arial;max-width:1000px;margin:20px auto;padding:20px;} .success{background:#e6ffe6;color:green;padding:10px;border-radius:4px;margin:10px 0;} .error{background:#ffe6e6;color:red;padding:10px;border-radius:4px;margin:10px 0;} .info{background:#e6f3ff;color:blue;padding:10px;border-radius:4px;margin:10px 0;} .warning{background:#fff3cd;color:#856404;padding:10px;border-radius:4px;margin:10px 0;} table{width:100%;border-collapse:collapse;margin:20px 0;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background:#f5f5f5;} .btn{display:inline-block;padding:8px 16px;margin:4px;text-decoration:none;border-radius:4px;color:white;} .btn-danger{background:#dc3545;} .btn-success{background:#28a745;}</style>";
echo "</head><body>";

echo "<h1>🔍 Check Existing SKUs</h1>";

try {
    require_once 'config/db_connect.php';
    
    if (isset($db_connection_error)) {
        echo "<div class='error'>❌ Database connection failed!</div>";
        exit;
    }
    
    echo "<div class='success'>✅ Database connected successfully!</div>";
    
    // Get all existing SKUs
    echo "<h2>📦 All Existing SKUs</h2>";
    
    $stmt = $conn->query("SELECT p.id, p.name, p.sku, p.supplier_id, u.fullname as supplier_name, p.created_at 
                          FROM products p 
                          JOIN users u ON p.supplier_id = u.id 
                          ORDER BY p.created_at DESC");
    $products = $stmt->fetchAll();
    
    if (empty($products)) {
        echo "<div class='info'>ℹ️ No products found in database</div>";
    } else {
        echo "<div class='info'>📊 Found " . count($products) . " products with SKUs</div>";
        
        echo "<table>";
        echo "<tr><th>ID</th><th>Product Name</th><th>SKU</th><th>Supplier</th><th>Created</th><th>Actions</th></tr>";
        
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td>{$product['id']}</td>";
            echo "<td>{$product['name']}</td>";
            echo "<td><strong>{$product['sku']}</strong></td>";
            echo "<td>{$product['supplier_name']}</td>";
            echo "<td>" . date('M j, Y H:i', strtotime($product['created_at'])) . "</td>";
            echo "<td>";
            echo "<a href='#' onclick='deleteProduct({$product['id']}, \"{$product['sku']}\")' class='btn btn-danger' style='font-size:12px;padding:4px 8px;'>Delete</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check for duplicate SKUs
    echo "<h2>🔍 Duplicate SKU Analysis</h2>";
    
    $stmt = $conn->query("SELECT sku, COUNT(*) as count FROM products GROUP BY sku HAVING COUNT(*) > 1");
    $duplicates = $stmt->fetchAll();
    
    if (empty($duplicates)) {
        echo "<div class='success'>✅ No duplicate SKUs found</div>";
    } else {
        echo "<div class='error'>❌ Found duplicate SKUs:</div>";
        echo "<table>";
        echo "<tr><th>SKU</th><th>Count</th><th>Action</th></tr>";
        foreach ($duplicates as $dup) {
            echo "<tr>";
            echo "<td><strong>{$dup['sku']}</strong></td>";
            echo "<td>{$dup['count']}</td>";
            echo "<td><a href='#' onclick='fixDuplicateSKU(\"{$dup['sku']}\")' class='btn btn-success' style='font-size:12px;padding:4px 8px;'>Fix</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // SKU pattern analysis
    echo "<h2>📊 SKU Pattern Analysis</h2>";
    
    $stmt = $conn->query("SELECT 
                            SUBSTRING(sku, 1, 2) as prefix,
                            COUNT(*) as count
                          FROM products 
                          WHERE sku REGEXP '^[A-Z]{2}-'
                          GROUP BY SUBSTRING(sku, 1, 2)
                          ORDER BY count DESC");
    $patterns = $stmt->fetchAll();
    
    if (!empty($patterns)) {
        echo "<div class='info'>📈 SKU Prefix Usage:</div>";
        echo "<table>";
        echo "<tr><th>Prefix</th><th>Count</th><th>Category</th></tr>";
        foreach ($patterns as $pattern) {
            $category = '';
            switch ($pattern['prefix']) {
                case 'FD': $category = 'Feed'; break;
                case 'EQ': $category = 'Equipment'; break;
                case 'MD': $category = 'Medicine'; break;
                case 'SP': $category = 'Supplements'; break;
                case 'OT': $category = 'Other'; break;
                default: $category = 'Unknown';
            }
            echo "<tr>";
            echo "<td><strong>{$pattern['prefix']}</strong></td>";
            echo "<td>{$pattern['count']}</td>";
            echo "<td>{$category}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Generate suggested unique SKUs
    echo "<h2>💡 Generate Unique SKU Suggestions</h2>";
    
    $categories = ['feed' => 'FD', 'equipment' => 'EQ', 'medicine' => 'MD', 'supplements' => 'SP', 'other' => 'OT'];
    
    echo "<div class='info'>";
    echo "<strong>Suggested unique SKUs for new products:</strong><br><br>";
    
    foreach ($categories as $category => $prefix) {
        // Find next available number for this prefix
        $stmt = $conn->prepare("SELECT sku FROM products WHERE sku LIKE ? ORDER BY sku DESC LIMIT 1");
        $stmt->execute([$prefix . '-%']);
        $lastSku = $stmt->fetchColumn();
        
        if ($lastSku) {
            // Extract number and increment
            preg_match('/(\d+)$/', $lastSku, $matches);
            $nextNumber = isset($matches[1]) ? intval($matches[1]) + 1 : 1;
        } else {
            $nextNumber = 1;
        }
        
        $suggestedSku = $prefix . '-' . str_pad($nextNumber, 3, '0', STR_PAD_LEFT);
        echo "<strong>" . ucfirst($category) . ":</strong> <code>$suggestedSku</code><br>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}

echo "<h2>🛠️ Quick Actions</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        try {
            if ($_POST['action'] === 'delete_product' && isset($_POST['product_id'])) {
                $productId = intval($_POST['product_id']);
                $stmt = $conn->prepare("DELETE FROM products WHERE id = ?");
                $stmt->execute([$productId]);
                echo "<div class='success'>✅ Product deleted successfully!</div>";
                echo "<script>setTimeout(() => window.location.reload(), 1000);</script>";
            }
            
            if ($_POST['action'] === 'fix_duplicate_sku' && isset($_POST['sku'])) {
                $duplicateSku = $_POST['sku'];
                
                // Get all products with this SKU
                $stmt = $conn->prepare("SELECT id, name FROM products WHERE sku = ? ORDER BY id");
                $stmt->execute([$duplicateSku]);
                $duplicateProducts = $stmt->fetchAll();
                
                // Keep the first one, rename the others
                $counter = 1;
                foreach ($duplicateProducts as $index => $product) {
                    if ($index > 0) { // Skip the first one
                        $newSku = $duplicateSku . '-' . $counter;
                        
                        // Make sure the new SKU doesn't exist
                        while (true) {
                            $stmt = $conn->prepare("SELECT COUNT(*) FROM products WHERE sku = ?");
                            $stmt->execute([$newSku]);
                            if ($stmt->fetchColumn() == 0) break;
                            $counter++;
                            $newSku = $duplicateSku . '-' . $counter;
                        }
                        
                        // Update the SKU
                        $stmt = $conn->prepare("UPDATE products SET sku = ? WHERE id = ?");
                        $stmt->execute([$newSku, $product['id']]);
                        
                        echo "<div class='success'>✅ Updated product '{$product['name']}' SKU to: $newSku</div>";
                        $counter++;
                    }
                }
                echo "<script>setTimeout(() => window.location.reload(), 2000);</script>";
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ Action failed: " . $e->getMessage() . "</div>";
        }
    }
}

echo "<div style='margin:20px 0;'>";
echo "<a href='supplier/add_product.php' class='btn btn-success'>Add New Product</a> ";
echo "<a href='supplier/products.php' class='btn' style='background:#007bff;'>View Products</a> ";
echo "<a href='?' class='btn' style='background:#6c757d;'>Refresh</a>";
echo "</div>";

echo "<div class='warning'>";
echo "<strong>⚠️ Important:</strong> If you're getting SKU duplication errors:<br>";
echo "1. Use the suggested SKUs above<br>";
echo "2. Delete duplicate products if needed<br>";
echo "3. The system will auto-generate unique SKUs for new products<br>";
echo "4. Manual SKU entry should follow the pattern: PREFIX-NUMBER (e.g., FD-001)";
echo "</div>";

echo "<script>";
echo "function deleteProduct(id, sku) {";
echo "  if (confirm('Are you sure you want to delete the product with SKU: ' + sku + '?')) {";
echo "    const form = document.createElement('form');";
echo "    form.method = 'POST';";
echo "    form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"delete_product\"><input type=\"hidden\" name=\"product_id\" value=\"' + id + '\">';";
echo "    document.body.appendChild(form);";
echo "    form.submit();";
echo "  }";
echo "}";

echo "function fixDuplicateSKU(sku) {";
echo "  if (confirm('Fix duplicate SKU: ' + sku + '? This will rename duplicate entries.')) {";
echo "    const form = document.createElement('form');";
echo "    form.method = 'POST';";
echo "    form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"fix_duplicate_sku\"><input type=\"hidden\" name=\"sku\" value=\"' + sku + '\">';";
echo "    document.body.appendChild(form);";
echo "    form.submit();";
echo "  }";
echo "}";
echo "</script>";

echo "</body></html>";
?>
