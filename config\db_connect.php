<?php
// Database connection parameters
$host = 'localhost';
$dbname = 'pigit_db';
$username = 'root';
$password = '';

// Create connection
try {
    $conn = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    // Set the PDO error mode to exception
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    // Set default fetch mode to associative array
    $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    // If connection fails, log the error
    error_log("Connection failed: " . $e->getMessage());
    // You can set a flag to indicate connection failure
    $db_connection_error = true;
}
?>
