<?php
// Database connection parameters
$host = 'localhost';
$username = 'root';
$password = '';

try {
    // Create connection without database selection
    $conn = new PDO("mysql:host=$host", $username, $password);
    // Set the PDO error mode to exception
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database if it doesn't exist
    $sql = "CREATE DATABASE IF NOT EXISTS pigit_db";
    $conn->exec($sql);
    echo "Database created successfully or already exists<br>";
    
    // Select the database
    $conn->exec("USE pigit_db");
    
    // Create users table with role field
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        fullname VARCHAR(100) NOT NULL,
        username VARCHAR(50) NOT NULL UNIQUE,
        email VARCHAR(100) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role ENUM('farmer', 'supplier', 'retailer', 'admin') NOT NULL DEFAULT 'farmer',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    $conn->exec($sql);
    echo "Users table created successfully or already exists<br>";
    
    // Create an admin user if it doesn't exist
    $check = $conn->query("SELECT COUNT(*) FROM users WHERE username = 'admin'")->fetchColumn();
    
    if ($check == 0) {
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (fullname, username, email, password, role) 
                VALUES ('Admin User', 'admin', '<EMAIL>', :password, 'admin')";
        $stmt = $conn->prepare($sql);
        $stmt->bindParam(':password', $admin_password);
        $stmt->execute();
        echo "Admin user created successfully<br>";
    } else {
        echo "Admin user already exists<br>";
    }
    
    echo "Database setup completed successfully!";
    
} catch(PDOException $e) {
    echo "Error: " . $e->getMessage();
}

// Close connection
$conn = null;
?>
