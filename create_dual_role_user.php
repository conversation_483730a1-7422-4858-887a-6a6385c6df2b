<?php
// <PERSON>ript to create a user with dual supplier/retailer capabilities
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html><head><title>Create Dual Role User</title>";
echo "<style>body{font-family:Arial;max-width:800px;margin:20px auto;padding:20px;} .success{background:#e6ffe6;color:green;padding:10px;border-radius:4px;margin:10px 0;} .error{background:#ffe6e6;color:red;padding:10px;border-radius:4px;margin:10px 0;} .info{background:#e6f3ff;color:blue;padding:10px;border-radius:4px;margin:10px 0;} .warning{background:#fff3cd;color:#856404;padding:10px;border-radius:4px;margin:10px 0;} .btn{display:inline-block;padding:8px 16px;margin:4px;text-decoration:none;border-radius:4px;color:white;} .btn-primary{background:#007bff;} .btn-success{background:#28a745;}</style>";
echo "</head><body>";

echo "<h1>👥 Create Dual Role User (Supplier + Retailer)</h1>";

echo "<div class='info'>";
echo "<strong>Note:</strong> In the current system design, users have a single role ('supplier' or 'retailer'). ";
echo "However, we can create a user with 'supplier' role who can access both supplier and retailer functions.";
echo "</div>";

try {
    require_once 'config/db_connect.php';
    
    if (isset($db_connection_error)) {
        echo "<div class='error'>❌ Database connection failed!</div>";
        exit;
    }
    
    echo "<div class='success'>✅ Database connected successfully!</div>";
    
    // Handle form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_user'])) {
        $username = trim($_POST['username']);
        $fullname = trim($_POST['fullname']);
        $email = trim($_POST['email']);
        $password = $_POST['password'];
        $company_name = trim($_POST['company_name']);
        $role = $_POST['role']; // Will be 'supplier' for dual access
        
        // Validation
        $errors = [];
        if (empty($username)) $errors[] = "Username is required";
        if (empty($fullname)) $errors[] = "Full name is required";
        if (empty($email)) $errors[] = "Email is required";
        if (empty($password)) $errors[] = "Password is required";
        if (empty($company_name)) $errors[] = "Company name is required";
        
        // Check if username exists
        if (empty($errors)) {
            $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
            $stmt->execute([$username]);
            if ($stmt->fetchColumn() > 0) {
                $errors[] = "Username already exists";
            }
        }
        
        // Check if email exists
        if (empty($errors)) {
            $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->fetchColumn() > 0) {
                $errors[] = "Email already exists";
            }
        }
        
        if (empty($errors)) {
            try {
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                
                $sql = "INSERT INTO users (fullname, username, email, password, role, company_name, business_type) 
                        VALUES (?, ?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($sql);
                $stmt->execute([
                    $fullname,
                    $username,
                    $email,
                    $hashedPassword,
                    $role,
                    $company_name,
                    'Supplier & Retailer'
                ]);
                
                $userId = $conn->lastInsertId();
                
                echo "<div class='success'>✅ User created successfully!</div>";
                echo "<div class='info'>";
                echo "<strong>User Details:</strong><br>";
                echo "• ID: $userId<br>";
                echo "• Username: $username<br>";
                echo "• Full Name: $fullname<br>";
                echo "• Email: $email<br>";
                echo "• Role: $role<br>";
                echo "• Company: $company_name<br>";
                echo "</div>";
                
                // Auto-login the new user
                session_start();
                $_SESSION['user_id'] = $userId;
                $_SESSION['username'] = $username;
                $_SESSION['fullname'] = $fullname;
                $_SESSION['email'] = $email;
                $_SESSION['role'] = $role;
                $_SESSION['company_name'] = $company_name;
                $_SESSION['business_type'] = 'Supplier & Retailer';
                $_SESSION['login_time'] = time();
                
                echo "<div class='success'>✅ Automatically logged in as $username</div>";
                
            } catch (PDOException $e) {
                echo "<div class='error'>❌ Error creating user: " . $e->getMessage() . "</div>";
            }
        } else {
            echo "<div class='error'>❌ Validation errors:<br>";
            foreach ($errors as $error) {
                echo "• $error<br>";
            }
            echo "</div>";
        }
    }
    
    // Show current session if exists
    session_start();
    if (isset($_SESSION['user_id'])) {
        echo "<h2>🔐 Current Session</h2>";
        echo "<div class='info'>";
        echo "<strong>Logged in as:</strong> {$_SESSION['fullname']} ({$_SESSION['username']})<br>";
        echo "<strong>Role:</strong> {$_SESSION['role']}<br>";
        echo "<strong>Company:</strong> {$_SESSION['company_name']}<br>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}

// Show form if no user created or if there were errors
if (!isset($_POST['create_user']) || !empty($errors ?? [])) {
    echo "<h2>📝 Create New Dual Role User</h2>";
    
    echo "<form method='POST' style='background:#f8f9fa;padding:20px;border-radius:8px;'>";
    echo "<div style='margin-bottom:15px;'>";
    echo "<label style='display:block;margin-bottom:5px;font-weight:bold;'>Username:</label>";
    echo "<input type='text' name='username' required style='width:100%;padding:8px;border:1px solid #ddd;border-radius:4px;' placeholder='e.g., dualuser'>";
    echo "</div>";
    
    echo "<div style='margin-bottom:15px;'>";
    echo "<label style='display:block;margin-bottom:5px;font-weight:bold;'>Full Name:</label>";
    echo "<input type='text' name='fullname' required style='width:100%;padding:8px;border:1px solid #ddd;border-radius:4px;' placeholder='e.g., John Doe'>";
    echo "</div>";
    
    echo "<div style='margin-bottom:15px;'>";
    echo "<label style='display:block;margin-bottom:5px;font-weight:bold;'>Email:</label>";
    echo "<input type='email' name='email' required style='width:100%;padding:8px;border:1px solid #ddd;border-radius:4px;' placeholder='e.g., <EMAIL>'>";
    echo "</div>";
    
    echo "<div style='margin-bottom:15px;'>";
    echo "<label style='display:block;margin-bottom:5px;font-weight:bold;'>Password:</label>";
    echo "<input type='password' name='password' required style='width:100%;padding:8px;border:1px solid #ddd;border-radius:4px;' placeholder='Enter password'>";
    echo "</div>";
    
    echo "<div style='margin-bottom:15px;'>";
    echo "<label style='display:block;margin-bottom:5px;font-weight:bold;'>Company Name:</label>";
    echo "<input type='text' name='company_name' required style='width:100%;padding:8px;border:1px solid #ddd;border-radius:4px;' placeholder='e.g., ABC Supply & Retail Co.'>";
    echo "</div>";
    
    echo "<div style='margin-bottom:15px;'>";
    echo "<label style='display:block;margin-bottom:5px;font-weight:bold;'>Primary Role:</label>";
    echo "<select name='role' required style='width:100%;padding:8px;border:1px solid #ddd;border-radius:4px;'>";
    echo "<option value='supplier'>Supplier (Can access both supplier and retailer functions)</option>";
    echo "<option value='retailer'>Retailer (Limited to retailer functions)</option>";
    echo "</select>";
    echo "<small style='color:#666;'>Note: Choose 'Supplier' for dual access capabilities</small>";
    echo "</div>";
    
    echo "<button type='submit' name='create_user' class='btn btn-success'>Create Dual Role User</button>";
    echo "</form>";
}

echo "<h2>🎯 Quick Actions</h2>";
echo "<div style='margin:20px 0;'>";
echo "<a href='debug_user_roles.php' class='btn btn-primary'>Debug User Roles</a> ";
echo "<a href='supplier/add_product.php' class='btn btn-success'>Test Add Product</a> ";
echo "<a href='login.php' class='btn btn-primary'>Login Page</a> ";
echo "<a href='logout.php' class='btn' style='background:#dc3545;'>Logout</a>";
echo "</div>";

echo "<div class='warning'>";
echo "<strong>💡 Understanding Roles:</strong><br>";
echo "• <strong>Supplier Role:</strong> Can add/edit products, manage orders, view all supplier functions<br>";
echo "• <strong>Retailer Role:</strong> Can browse products, place orders, view retailer functions<br>";
echo "• <strong>Dual Access:</strong> Use 'supplier' role for users who need both capabilities<br>";
echo "• The system checks for exact role match, so 'supplier' role is required for product management";
echo "</div>";

echo "</body></html>";
?>
