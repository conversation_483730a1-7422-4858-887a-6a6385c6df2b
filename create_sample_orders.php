<?php
// <PERSON>ript to create sample orders for testing
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html><head><title>Create Sample Orders</title>";
echo "<style>body{font-family:Arial;max-width:800px;margin:20px auto;padding:20px;} .success{background:#e6ffe6;color:green;padding:10px;border-radius:4px;margin:10px 0;} .error{background:#ffe6e6;color:red;padding:10px;border-radius:4px;margin:10px 0;} .info{background:#e6f3ff;color:blue;padding:10px;border-radius:4px;margin:10px 0;}</style>";
echo "</head><body>";

echo "<h1>🛒 Create Sample Orders</h1>";

try {
    require_once 'config/db_connect.php';
    
    if (isset($db_connection_error)) {
        echo "<div class='error'>❌ Database connection failed!</div>";
        exit;
    }
    
    echo "<div class='success'>✅ Database connected successfully!</div>";
    
    // Check if we have suppliers and retailers
    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'supplier'");
    $supplierCount = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'retailer'");
    $retailerCount = $stmt->fetchColumn();
    
    $stmt = $conn->query("SELECT COUNT(*) FROM products");
    $productCount = $stmt->fetchColumn();
    
    echo "<div class='info'>📊 Current Data: $supplierCount suppliers, $retailerCount retailers, $productCount products</div>";
    
    if ($supplierCount == 0 || $retailerCount == 0 || $productCount == 0) {
        echo "<div class='error'>❌ Need at least 1 supplier, 1 retailer, and 1 product to create orders</div>";
        echo "<p><a href='create_test_users.php'>Create Test Users</a> | <a href='setup_supplier_retailer_db.php'>Setup Database</a></p>";
        exit;
    }
    
    // Get suppliers and retailers
    $stmt = $conn->query("SELECT id, fullname FROM users WHERE role = 'supplier'");
    $suppliers = $stmt->fetchAll();
    
    $stmt = $conn->query("SELECT id, fullname FROM users WHERE role = 'retailer'");
    $retailers = $stmt->fetchAll();
    
    // Get products
    $stmt = $conn->query("SELECT id, name, price, supplier_id FROM products WHERE status = 'active'");
    $products = $stmt->fetchAll();
    
    // Check if orders already exist
    $stmt = $conn->query("SELECT COUNT(*) FROM orders");
    $existingOrders = $stmt->fetchColumn();
    
    if ($existingOrders > 0) {
        echo "<div class='info'>ℹ️ Found $existingOrders existing orders. Creating additional sample orders...</div>";
    }
    
    // Create sample orders
    $sampleOrders = [
        [
            'status' => 'pending',
            'items' => 2,
            'notes' => 'Urgent delivery needed for weekend event'
        ],
        [
            'status' => 'confirmed',
            'items' => 1,
            'notes' => 'Regular monthly order'
        ],
        [
            'status' => 'processing',
            'items' => 3,
            'notes' => 'Please pack carefully - fragile items'
        ],
        [
            'status' => 'shipped',
            'items' => 1,
            'notes' => 'Express shipping requested'
        ],
        [
            'status' => 'delivered',
            'items' => 2,
            'notes' => 'Customer very satisfied with previous orders'
        ]
    ];
    
    $ordersCreated = 0;
    
    foreach ($sampleOrders as $orderData) {
        try {
            // Pick random retailer and supplier
            $retailer = $retailers[array_rand($retailers)];
            $supplier = $suppliers[array_rand($suppliers)];
            
            // Get products from this supplier
            $supplierProducts = array_filter($products, function($p) use ($supplier) {
                return $p['supplier_id'] == $supplier['id'];
            });
            
            if (empty($supplierProducts)) {
                continue; // Skip if no products for this supplier
            }
            
            // Generate order number
            $orderNumber = 'ORD-' . date('Y') . '-' . str_pad(($existingOrders + $ordersCreated + 1), 6, '0', STR_PAD_LEFT);
            
            // Calculate order total
            $totalAmount = 0;
            $selectedProducts = [];
            
            for ($i = 0; $i < $orderData['items']; $i++) {
                $product = $supplierProducts[array_rand($supplierProducts)];
                $quantity = rand(1, 5);
                $unitPrice = $product['price'];
                $totalPrice = $quantity * $unitPrice;
                
                $selectedProducts[] = [
                    'product_id' => $product['id'],
                    'quantity' => $quantity,
                    'unit_price' => $unitPrice,
                    'total_price' => $totalPrice
                ];
                
                $totalAmount += $totalPrice;
            }
            
            // Create shipping address
            $addresses = [
                "123 Main Street\nAnytown, ST 12345\nUnited States",
                "456 Oak Avenue\nSomewhere, ST 67890\nUnited States",
                "789 Pine Road\nAnywhere, ST 54321\nUnited States"
            ];
            $shippingAddress = $addresses[array_rand($addresses)];
            
            // Insert order
            $sql = "INSERT INTO orders (retailer_id, supplier_id, order_number, status, total_amount, shipping_address, notes, created_at, updated_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            // Random date within last 30 days
            $randomDays = rand(0, 30);
            $orderDate = date('Y-m-d H:i:s', strtotime("-$randomDays days"));
            
            $stmt = $conn->prepare($sql);
            $stmt->execute([
                $retailer['id'],
                $supplier['id'],
                $orderNumber,
                $orderData['status'],
                $totalAmount,
                $shippingAddress,
                $orderData['notes'],
                $orderDate,
                $orderDate
            ]);
            
            $orderId = $conn->lastInsertId();
            
            // Insert order items
            foreach ($selectedProducts as $item) {
                $sql = "INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price) 
                        VALUES (?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($sql);
                $stmt->execute([
                    $orderId,
                    $item['product_id'],
                    $item['quantity'],
                    $item['unit_price'],
                    $item['total_price']
                ]);
            }
            
            echo "<div class='success'>✅ Created order $orderNumber (Status: {$orderData['status']}, Total: $" . number_format($totalAmount, 2) . ")</div>";
            $ordersCreated++;
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ Error creating order: " . $e->getMessage() . "</div>";
        }
    }
    
    echo "<div class='success'>🎉 Successfully created $ordersCreated sample orders!</div>";
    
    // Show summary
    echo "<h2>📊 Order Summary</h2>";
    
    $stmt = $conn->query("
        SELECT status, COUNT(*) as count, SUM(total_amount) as total_value 
        FROM orders 
        GROUP BY status 
        ORDER BY 
            CASE status 
                WHEN 'pending' THEN 1 
                WHEN 'confirmed' THEN 2 
                WHEN 'processing' THEN 3 
                WHEN 'shipped' THEN 4 
                WHEN 'delivered' THEN 5 
                WHEN 'cancelled' THEN 6 
                ELSE 7 
            END
    ");
    $statusSummary = $stmt->fetchAll();
    
    echo "<table style='width:100%;border-collapse:collapse;margin:20px 0;'>";
    echo "<tr style='background:#f5f5f5;'><th style='border:1px solid #ddd;padding:8px;'>Status</th><th style='border:1px solid #ddd;padding:8px;'>Count</th><th style='border:1px solid #ddd;padding:8px;'>Total Value</th></tr>";
    
    foreach ($statusSummary as $row) {
        echo "<tr>";
        echo "<td style='border:1px solid #ddd;padding:8px;'>" . ucfirst($row['status']) . "</td>";
        echo "<td style='border:1px solid #ddd;padding:8px;'>" . $row['count'] . "</td>";
        echo "<td style='border:1px solid #ddd;padding:8px;'>$" . number_format($row['total_value'], 2) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}

echo "<h2>🎯 Next Steps</h2>";
echo "<div class='info'>";
echo "<p><strong>Test the orders system:</strong></p>";
echo "<a href='login.php' style='display:inline-block;padding:8px 16px;margin:4px;background:#007bff;color:white;text-decoration:none;border-radius:4px;'>Login as Supplier</a> ";
echo "<a href='supplier/orders.php' style='display:inline-block;padding:8px 16px;margin:4px;background:#28a745;color:white;text-decoration:none;border-radius:4px;'>View Orders</a> ";
echo "<a href='test_product_management.php' style='display:inline-block;padding:8px 16px;margin:4px;background:#ffc107;color:#212529;text-decoration:none;border-radius:4px;'>Test System</a>";
echo "</div>";

echo "<div class='info'>";
echo "<p><strong>Test Credentials:</strong></p>";
echo "<ul>";
echo "<li><strong>Supplier:</strong> Username: <code>testsupplier</code>, Password: <code>password123</code></li>";
echo "<li><strong>Retailer:</strong> Username: <code>testretailer</code>, Password: <code>password123</code></li>";
echo "</ul>";
echo "</div>";

echo "</body></html>";
?>
