<?php
// Create test users for login testing
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html><head><title>Create Test Users</title>";
echo "<style>body{font-family:Arial;max-width:800px;margin:20px auto;padding:20px;} .success{background:#e6ffe6;color:green;padding:10px;border-radius:4px;margin:10px 0;} .error{background:#ffe6e6;color:red;padding:10px;border-radius:4px;margin:10px 0;} .info{background:#e6f3ff;color:blue;padding:10px;border-radius:4px;margin:10px 0;}</style>";
echo "</head><body>";

echo "<h1>Create Test Users for Login Testing</h1>";

try {
    require_once 'config/db_connect.php';
    
    if (isset($db_connection_error)) {
        echo "<div class='error'>❌ Database connection failed!</div>";
        exit;
    }
    
    echo "<div class='success'>✅ Database connected successfully!</div>";
    
    // Test users to create
    $testUsers = [
        [
            'fullname' => 'Test Supplier User',
            'username' => 'testsupplier',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'supplier',
            'company_name' => 'Test Feed Supplies Inc.',
            'business_type' => 'feed_supplier',
            'phone' => '******-123-4567',
            'address' => '123 Supply Street, Business City, NY 10001'
        ],
        [
            'fullname' => 'Test Retailer User',
            'username' => 'testretailer',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'retailer',
            'company_name' => 'Test Grocery Chain',
            'business_type' => 'grocery_retailer',
            'phone' => '******-987-6543',
            'address' => '456 Retail Avenue, Shopping City, CA 90210'
        ],
        [
            'fullname' => 'Test Farmer User',
            'username' => 'testfarmer',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'role' => 'farmer',
            'company_name' => null,
            'business_type' => null,
            'phone' => '******-456-7890',
            'address' => '789 Farm Road, Rural County, TX 75001'
        ]
    ];
    
    echo "<h2>Creating Test Users</h2>";
    
    foreach ($testUsers as $user) {
        try {
            // Check if user already exists
            $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE username = :username OR email = :email");
            $stmt->bindParam(':username', $user['username']);
            $stmt->bindParam(':email', $user['email']);
            $stmt->execute();
            
            if ($stmt->fetchColumn() > 0) {
                echo "<div class='info'>ℹ️ User '{$user['username']}' already exists - skipping</div>";
                continue;
            }
            
            // Hash password
            $hashed_password = password_hash($user['password'], PASSWORD_DEFAULT);
            
            // Insert user
            $sql = "INSERT INTO users (fullname, username, email, password, role, company_name, business_type, phone, address) 
                    VALUES (:fullname, :username, :email, :password, :role, :company_name, :business_type, :phone, :address)";
            
            $stmt = $conn->prepare($sql);
            $stmt->bindParam(':fullname', $user['fullname']);
            $stmt->bindParam(':username', $user['username']);
            $stmt->bindParam(':email', $user['email']);
            $stmt->bindParam(':password', $hashed_password);
            $stmt->bindParam(':role', $user['role']);
            $stmt->bindParam(':company_name', $user['company_name']);
            $stmt->bindParam(':business_type', $user['business_type']);
            $stmt->bindParam(':phone', $user['phone']);
            $stmt->bindParam(':address', $user['address']);
            
            if ($stmt->execute()) {
                echo "<div class='success'>✅ Created {$user['role']}: {$user['username']} ({$user['email']})</div>";
            } else {
                echo "<div class='error'>❌ Failed to create user: {$user['username']}</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ Error creating user {$user['username']}: " . $e->getMessage() . "</div>";
        }
    }
    
    echo "<h2>Test Login Credentials</h2>";
    echo "<div class='info'>";
    echo "<p><strong>All test users use the password:</strong> <code>password123</code></p>";
    echo "<table border='1' style='border-collapse:collapse;width:100%;'>";
    echo "<tr style='background:#f0f0f0;'><th>Role</th><th>Username</th><th>Email</th><th>Company</th><th>Test Login</th></tr>";
    
    foreach ($testUsers as $user) {
        echo "<tr>";
        echo "<td><strong>" . ucfirst($user['role']) . "</strong></td>";
        echo "<td>" . $user['username'] . "</td>";
        echo "<td>" . $user['email'] . "</td>";
        echo "<td>" . ($user['company_name'] ?? 'N/A') . "</td>";
        echo "<td>";
        echo "<form action='login.php' method='POST' style='display:inline;'>";
        echo "<input type='hidden' name='username' value='" . $user['username'] . "'>";
        echo "<input type='hidden' name='password' value='password123'>";
        echo "<input type='hidden' name='login' value='1'>";
        echo "<button type='submit' style='padding:5px 10px;background:#f97316;color:white;border:none;border-radius:4px;cursor:pointer;'>Login</button>";
        echo "</form>";
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";
    
    // Show current users
    echo "<h2>All Users in Database</h2>";
    try {
        $stmt = $conn->query("SELECT id, fullname, username, email, role, company_name, created_at FROM users ORDER BY created_at DESC");
        $users = $stmt->fetchAll();
        
        if (count($users) > 0) {
            echo "<table border='1' style='border-collapse:collapse;width:100%;'>";
            echo "<tr style='background:#f0f0f0;'><th>ID</th><th>Name</th><th>Username</th><th>Email</th><th>Role</th><th>Company</th><th>Created</th></tr>";
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td>" . $user['id'] . "</td>";
                echo "<td>" . htmlspecialchars($user['fullname']) . "</td>";
                echo "<td>" . htmlspecialchars($user['username']) . "</td>";
                echo "<td>" . htmlspecialchars($user['email']) . "</td>";
                echo "<td><strong>" . ucfirst($user['role']) . "</strong></td>";
                echo "<td>" . htmlspecialchars($user['company_name'] ?? 'N/A') . "</td>";
                echo "<td>" . $user['created_at'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='info'>No users found in database.</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error fetching users: " . $e->getMessage() . "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Setup failed: " . $e->getMessage() . "</div>";
}

echo "<h2>Next Steps</h2>";
echo "<div class='info'>";
echo "<p><strong>Test users created!</strong> You can now:</p>";
echo "<ul>";
echo "<li><a href='login.php'>Go to Login Page</a></li>";
echo "<li><a href='test_login.php'>Use Login Test Suite</a></li>";
echo "<li><a href='supplier/dashboard.php'>Try accessing Supplier Dashboard directly (should redirect to login)</a></li>";
echo "</ul>";
echo "</div>";

echo "</body></html>";
?>
