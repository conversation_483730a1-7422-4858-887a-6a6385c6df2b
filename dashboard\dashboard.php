<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../login.php");
    exit();
}

$user_role = $_SESSION['role'];
$user_name = $_SESSION['fullname'];
$username = $_SESSION['username'];
$company_name = $_SESSION['company_name'] ?? null;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - PiGit</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>tailwind.config={theme:{extend:{colors:{primary:'#f97316',secondary:'#84cc16'}}}}</script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <style>
        .role-farmer { background: #fef3c7; color: #92400e; }
        .role-supplier { background: #dbeafe; color: #1e40af; }
        .role-retailer { background: #dcfce7; color: #166534; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="font-bold text-2xl text-primary">PiGit</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="role-<?php echo $user_role; ?> px-3 py-1 rounded-full text-sm font-medium">
                        <?php echo ucfirst($user_role); ?>
                    </span>
                    <span class="text-gray-700">Welcome, <?php echo htmlspecialchars($user_name); ?></span>
                    <a href="logout.php" class="text-red-600 hover:text-red-800">
                        <i class="ri-logout-circle-line"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Welcome to PiGit Dashboard</h1>
                <p class="text-gray-600 mt-2">You are logged in as: <strong><?php echo ucfirst($user_role); ?></strong></p>
                <?php if ($company_name): ?>
                    <p class="text-gray-600">Company: <strong><?php echo htmlspecialchars($company_name); ?></strong></p>
                <?php endif; ?>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="ri-user-line text-gray-400 ri-2x"></i>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Profile</dt>
                                    <dd class="text-lg font-medium text-gray-900"><?php echo htmlspecialchars($user_name); ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="ri-shield-user-line text-gray-400 ri-2x"></i>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Role</dt>
                                    <dd class="text-lg font-medium text-gray-900"><?php echo ucfirst($user_role); ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="ri-time-line text-gray-400 ri-2x"></i>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Login Time</dt>
                                    <dd class="text-lg font-medium text-gray-900"><?php echo date('H:i', $_SESSION['login_time']); ?></dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Role-Specific Dashboard</h2>
                <p class="text-gray-600 mb-4">Based on your role as a <strong><?php echo ucfirst($user_role); ?></strong>, here are your available options:</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <?php if ($user_role === 'farmer'): ?>
                        <a href="farmer_dashboard.php" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <i class="ri-plant-line text-green-600 ri-2x mb-2"></i>
                            <h3 class="font-medium">Farmer Dashboard</h3>
                            <p class="text-sm text-gray-600">Manage livestock, feed, and farm operations</p>
                        </a>
                    <?php elseif ($user_role === 'supplier'): ?>
                        <a href="supplier_dashboard.php" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <i class="ri-truck-line text-blue-600 ri-2x mb-2"></i>
                            <h3 class="font-medium">Supplier Dashboard</h3>
                            <p class="text-sm text-gray-600">Manage products, orders, and deliveries</p>
                        </a>
                    <?php elseif ($user_role === 'retailer'): ?>
                        <a href="retailer_dashboard.php" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <i class="ri-store-line text-green-600 ri-2x mb-2"></i>
                            <h3 class="font-medium">Retailer Dashboard</h3>
                            <p class="text-sm text-gray-600">Manage inventory, sales, and customers</p>
                        </a>
                    <?php endif; ?>
                    
                    <a href="#" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                        <i class="ri-settings-line text-gray-600 ri-2x mb-2"></i>
                        <h3 class="font-medium">Settings</h3>
                        <p class="text-sm text-gray-600">Manage your account settings</p>
                    </a>
                    
                    <a href="#" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                        <i class="ri-question-line text-gray-600 ri-2x mb-2"></i>
                        <h3 class="font-medium">Help & Support</h3>
                        <p class="text-sm text-gray-600">Get help and contact support</p>
                    </a>
                </div>
            </div>

            <div class="mt-8 bg-white shadow rounded-lg p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Session Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h3 class="font-medium text-gray-900">User Details</h3>
                        <ul class="mt-2 text-sm text-gray-600 space-y-1">
                            <li><strong>ID:</strong> <?php echo $_SESSION['user_id']; ?></li>
                            <li><strong>Username:</strong> <?php echo htmlspecialchars($_SESSION['username']); ?></li>
                            <li><strong>Email:</strong> <?php echo htmlspecialchars($_SESSION['email']); ?></li>
                            <li><strong>Role:</strong> <?php echo ucfirst($_SESSION['role']); ?></li>
                        </ul>
                    </div>
                    
                    <?php if ($user_role !== 'farmer'): ?>
                    <div>
                        <h3 class="font-medium text-gray-900">Business Details</h3>
                        <ul class="mt-2 text-sm text-gray-600 space-y-1">
                            <li><strong>Company:</strong> <?php echo htmlspecialchars($_SESSION['company_name'] ?? 'N/A'); ?></li>
                            <li><strong>Business Type:</strong> <?php echo htmlspecialchars($_SESSION['business_type'] ?? 'N/A'); ?></li>
                            <li><strong>Phone:</strong> <?php echo htmlspecialchars($_SESSION['phone'] ?? 'N/A'); ?></li>
                        </ul>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
