<?php
// Debug script for user roles and supplier account issues
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html><head><title>Debug User Roles</title>";
echo "<style>body{font-family:Arial;max-width:1000px;margin:20px auto;padding:20px;} .success{background:#e6ffe6;color:green;padding:10px;border-radius:4px;margin:10px 0;} .error{background:#ffe6e6;color:red;padding:10px;border-radius:4px;margin:10px 0;} .info{background:#e6f3ff;color:blue;padding:10px;border-radius:4px;margin:10px 0;} .warning{background:#fff3cd;color:#856404;padding:10px;border-radius:4px;margin:10px 0;} table{width:100%;border-collapse:collapse;margin:20px 0;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background:#f5f5f5;} .btn{display:inline-block;padding:8px 16px;margin:4px;text-decoration:none;border-radius:4px;color:white;} .btn-primary{background:#007bff;} .btn-success{background:#28a745;} .btn-warning{background:#ffc107;color:#212529;} .btn-danger{background:#dc3545;}</style>";
echo "</head><body>";

echo "<h1>🔍 Debug User Roles & Supplier Account Issues</h1>";

try {
    require_once 'config/db_connect.php';
    
    if (isset($db_connection_error)) {
        echo "<div class='error'>❌ Database connection failed!</div>";
        exit;
    }
    
    echo "<div class='success'>✅ Database connected successfully!</div>";
    
    // Check current session
    session_start();
    
    echo "<h2>🔐 Current Session Information</h2>";
    
    if (isset($_SESSION['user_id'])) {
        echo "<div class='info'>";
        echo "<strong>Session Data:</strong><br>";
        echo "• User ID: " . $_SESSION['user_id'] . "<br>";
        echo "• Username: " . ($_SESSION['username'] ?? 'Not set') . "<br>";
        echo "• Full Name: " . ($_SESSION['fullname'] ?? 'Not set') . "<br>";
        echo "• Email: " . ($_SESSION['email'] ?? 'Not set') . "<br>";
        echo "• Role: " . ($_SESSION['role'] ?? 'Not set') . "<br>";
        echo "• Company: " . ($_SESSION['company_name'] ?? 'Not set') . "<br>";
        echo "</div>";
        
        // Verify session user in database
        $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $sessionUser = $stmt->fetch();
        
        if ($sessionUser) {
            echo "<div class='success'>✅ Session user found in database</div>";
            
            echo "<h3>📋 Database User Information</h3>";
            echo "<table>";
            echo "<tr><th>Field</th><th>Session Value</th><th>Database Value</th><th>Match</th></tr>";
            
            $fields = ['id' => 'user_id', 'username' => 'username', 'fullname' => 'fullname', 'email' => 'email', 'role' => 'role'];
            
            foreach ($fields as $dbField => $sessionField) {
                $sessionValue = $_SESSION[$sessionField] ?? 'Not set';
                $dbValue = $sessionUser[$dbField] ?? 'Not set';
                $match = ($sessionValue === $dbValue) ? '✅' : '❌';
                
                echo "<tr>";
                echo "<td><strong>$dbField</strong></td>";
                echo "<td>$sessionValue</td>";
                echo "<td>$dbValue</td>";
                echo "<td>$match</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Check role specifically
            if ($sessionUser['role'] !== 'supplier') {
                echo "<div class='error'>❌ User role is '{$sessionUser['role']}', not 'supplier'</div>";
                echo "<div class='warning'>This is why you're getting the 'Invalid supplier account' error.</div>";
            } else {
                echo "<div class='success'>✅ User has correct 'supplier' role</div>";
            }
            
        } else {
            echo "<div class='error'>❌ Session user ID {$_SESSION['user_id']} not found in database!</div>";
        }
        
    } else {
        echo "<div class='warning'>⚠️ No active session found</div>";
    }
    
    // Check all users and their roles
    echo "<h2>👥 All Users in Database</h2>";
    
    $stmt = $conn->query("SELECT id, username, fullname, email, role, company_name, business_type, created_at FROM users ORDER BY id");
    $users = $stmt->fetchAll();
    
    if (empty($users)) {
        echo "<div class='warning'>⚠️ No users found in database!</div>";
    } else {
        echo "<table>";
        echo "<tr><th>ID</th><th>Username</th><th>Full Name</th><th>Email</th><th>Role</th><th>Company</th><th>Business Type</th><th>Actions</th></tr>";
        
        foreach ($users as $user) {
            $rowClass = '';
            if ($user['role'] === 'supplier') {
                $rowClass = 'style="background-color:#e6ffe6;"';
            } elseif ($user['role'] === 'retailer') {
                $rowClass = 'style="background-color:#e6f3ff;"';
            } else {
                $rowClass = 'style="background-color:#fff3cd;"';
            }
            
            echo "<tr $rowClass>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['fullname']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td><strong>{$user['role']}</strong></td>";
            echo "<td>{$user['company_name']}</td>";
            echo "<td>{$user['business_type']}</td>";
            echo "<td>";
            if ($user['role'] !== 'supplier') {
                echo "<a href='#' onclick='makeSupplier({$user['id']}, \"{$user['username']}\")' class='btn btn-success' style='font-size:12px;padding:4px 8px;'>Make Supplier</a> ";
            }
            if ($user['role'] !== 'retailer') {
                echo "<a href='#' onclick='makeRetailer({$user['id']}, \"{$user['username']}\")' class='btn btn-primary' style='font-size:12px;padding:4px 8px;'>Make Retailer</a> ";
            }
            echo "<a href='#' onclick='loginAs({$user['id']}, \"{$user['username']}\")' class='btn btn-warning' style='font-size:12px;padding:4px 8px;'>Login As</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Role statistics
        $stmt = $conn->query("SELECT role, COUNT(*) as count FROM users GROUP BY role");
        $roleStats = $stmt->fetchAll();
        
        echo "<div class='info'>";
        echo "<strong>Role Statistics:</strong><br>";
        foreach ($roleStats as $stat) {
            echo "• {$stat['role']}: {$stat['count']} users<br>";
        }
        echo "</div>";
    }
    
    // Check for role issues
    echo "<h2>🔍 Role Analysis</h2>";
    
    // Check for users with invalid roles
    $stmt = $conn->query("SELECT * FROM users WHERE role NOT IN ('supplier', 'retailer', 'admin')");
    $invalidRoles = $stmt->fetchAll();
    
    if (!empty($invalidRoles)) {
        echo "<div class='error'>❌ Found users with invalid roles:</div>";
        echo "<table>";
        echo "<tr><th>ID</th><th>Username</th><th>Invalid Role</th><th>Action</th></tr>";
        foreach ($invalidRoles as $user) {
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td><strong>{$user['role']}</strong></td>";
            echo "<td><a href='#' onclick='fixRole({$user['id']})' class='btn btn-danger' style='font-size:12px;padding:4px 8px;'>Fix Role</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='success'>✅ All users have valid roles</div>";
    }
    
    // Check users table structure
    echo "<h2>🗄️ Users Table Structure</h2>";
    
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    
    echo "<table>";
    echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}

// Handle POST requests for role changes
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['action'])) {
            $userId = intval($_POST['user_id']);
            
            switch ($_POST['action']) {
                case 'make_supplier':
                    $stmt = $conn->prepare("UPDATE users SET role = 'supplier' WHERE id = ?");
                    $stmt->execute([$userId]);
                    echo "<div class='success'>✅ User updated to supplier role!</div>";
                    break;
                    
                case 'make_retailer':
                    $stmt = $conn->prepare("UPDATE users SET role = 'retailer' WHERE id = ?");
                    $stmt->execute([$userId]);
                    echo "<div class='success'>✅ User updated to retailer role!</div>";
                    break;
                    
                case 'login_as':
                    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
                    $stmt->execute([$userId]);
                    $user = $stmt->fetch();
                    
                    if ($user) {
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['username'] = $user['username'];
                        $_SESSION['fullname'] = $user['fullname'];
                        $_SESSION['email'] = $user['email'];
                        $_SESSION['role'] = $user['role'];
                        $_SESSION['company_name'] = $user['company_name'];
                        $_SESSION['business_type'] = $user['business_type'];
                        $_SESSION['login_time'] = time();
                        
                        echo "<div class='success'>✅ Logged in as {$user['username']} ({$user['role']})!</div>";
                        echo "<script>setTimeout(() => window.location.reload(), 1000);</script>";
                    }
                    break;
                    
                case 'fix_role':
                    // Default to supplier for invalid roles
                    $stmt = $conn->prepare("UPDATE users SET role = 'supplier' WHERE id = ?");
                    $stmt->execute([$userId]);
                    echo "<div class='success'>✅ Fixed invalid role - set to supplier!</div>";
                    break;
            }
            echo "<script>setTimeout(() => window.location.reload(), 1500);</script>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Action failed: " . $e->getMessage() . "</div>";
    }
}

echo "<h2>🛠️ Quick Actions</h2>";
echo "<div style='margin:20px 0;'>";
echo "<a href='logout.php' class='btn btn-danger'>Logout Current Session</a> ";
echo "<a href='login.php' class='btn btn-primary'>Login Page</a> ";
echo "<a href='supplier/add_product.php' class='btn btn-success'>Test Add Product</a> ";
echo "<a href='create_test_users.php' class='btn btn-warning'>Create Test Users</a> ";
echo "<a href='?' class='btn' style='background:#6c757d;'>Refresh</a>";
echo "</div>";

echo "<div class='warning'>";
echo "<strong>💡 Common Solutions:</strong><br>";
echo "1. If user role is not 'supplier', use the 'Make Supplier' button above<br>";
echo "2. If session data doesn't match database, logout and login again<br>";
echo "3. If no users exist, create test users<br>";
echo "4. Use 'Login As' to quickly switch between users for testing";
echo "</div>";

echo "<script>";
echo "function makeSupplier(userId, username) {";
echo "  if (confirm('Make user \"' + username + '\" a supplier?')) {";
echo "    const form = document.createElement('form');";
echo "    form.method = 'POST';";
echo "    form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"make_supplier\"><input type=\"hidden\" name=\"user_id\" value=\"' + userId + '\">';";
echo "    document.body.appendChild(form);";
echo "    form.submit();";
echo "  }";
echo "}";

echo "function makeRetailer(userId, username) {";
echo "  if (confirm('Make user \"' + username + '\" a retailer?')) {";
echo "    const form = document.createElement('form');";
echo "    form.method = 'POST';";
echo "    form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"make_retailer\"><input type=\"hidden\" name=\"user_id\" value=\"' + userId + '\">';";
echo "    document.body.appendChild(form);";
echo "    form.submit();";
echo "  }";
echo "}";

echo "function loginAs(userId, username) {";
echo "  if (confirm('Login as user \"' + username + '\"?')) {";
echo "    const form = document.createElement('form');";
echo "    form.method = 'POST';";
echo "    form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"login_as\"><input type=\"hidden\" name=\"user_id\" value=\"' + userId + '\">';";
echo "    document.body.appendChild(form);";
echo "    form.submit();";
echo "  }";
echo "}";

echo "function fixRole(userId) {";
echo "  if (confirm('Fix invalid role for this user?')) {";
echo "    const form = document.createElement('form');";
echo "    form.method = 'POST';";
echo "    form.innerHTML = '<input type=\"hidden\" name=\"action\" value=\"fix_role\"><input type=\"hidden\" name=\"user_id\" value=\"' + userId + '\">';";
echo "    document.body.appendChild(form);";
echo "    form.submit();";
echo "  }";
echo "}";
echo "</script>";

echo "</body></html>";
?>
