<?php
// <PERSON>rip<PERSON> to help enable GD extension in XAMPP
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html><head><title>Enable GD Extension</title>";
echo "<style>body{font-family:Arial;max-width:800px;margin:20px auto;padding:20px;} .success{background:#e6ffe6;color:green;padding:10px;border-radius:4px;margin:10px 0;} .error{background:#ffe6e6;color:red;padding:10px;border-radius:4px;margin:10px 0;} .info{background:#e6f3ff;color:blue;padding:10px;border-radius:4px;margin:10px 0;} .warning{background:#fff3cd;color:#856404;padding:10px;border-radius:4px;margin:10px 0;} .code{background:#f8f9fa;border:1px solid #dee2e6;padding:10px;border-radius:4px;font-family:monospace;margin:10px 0;}</style>";
echo "</head><body>";

echo "<h1>🔧 Enable GD Extension for Image Processing</h1>";

// Check current GD status
echo "<h2>📊 Current Status</h2>";

if (extension_loaded('gd')) {
    echo "<div class='success'>✅ GD Extension is already enabled!</div>";
    
    $gdInfo = gd_info();
    echo "<div class='info'>";
    echo "<strong>GD Information:</strong><br>";
    echo "GD Version: " . $gdInfo['GD Version'] . "<br>";
    echo "JPEG Support: " . ($gdInfo['JPEG Support'] ? 'Yes' : 'No') . "<br>";
    echo "PNG Support: " . ($gdInfo['PNG Support'] ? 'Yes' : 'No') . "<br>";
    echo "GIF Support: " . ($gdInfo['GIF Read Support'] ? 'Yes' : 'No') . "<br>";
    echo "WebP Support: " . (isset($gdInfo['WebP Support']) && $gdInfo['WebP Support'] ? 'Yes' : 'No') . "<br>";
    echo "</div>";
    
    echo "<div class='success'>🎉 Your system is ready for image processing!</div>";
    echo "<p><a href='test_image_upload.php' style='background:#28a745;color:white;padding:10px 20px;text-decoration:none;border-radius:4px;'>Test Image Upload System</a></p>";
    
} else {
    echo "<div class='error'>❌ GD Extension is NOT enabled</div>";
    
    echo "<h2>🛠️ How to Enable GD Extension in XAMPP</h2>";
    
    echo "<div class='warning'>";
    echo "<strong>⚠️ Important:</strong> You need to enable the GD extension to use image processing features like resizing and thumbnail generation.";
    echo "</div>";
    
    echo "<h3>Method 1: Using XAMPP Control Panel (Recommended)</h3>";
    echo "<ol>";
    echo "<li>Open XAMPP Control Panel</li>";
    echo "<li>Click on 'Config' button next to Apache</li>";
    echo "<li>Select 'PHP (php.ini)'</li>";
    echo "<li>Find the line: <code>;extension=gd</code></li>";
    echo "<li>Remove the semicolon (;) to uncomment it: <code>extension=gd</code></li>";
    echo "<li>Save the file</li>";
    echo "<li>Restart Apache server</li>";
    echo "<li>Refresh this page to check if GD is enabled</li>";
    echo "</ol>";
    
    echo "<h3>Method 2: Manual php.ini Edit</h3>";
    echo "<div class='info'>";
    echo "<strong>PHP Configuration File Location:</strong><br>";
    echo "File: " . php_ini_loaded_file() . "<br>";
    echo "Scan Dir: " . php_ini_scanned_files();
    echo "</div>";
    
    echo "<ol>";
    echo "<li>Open the php.ini file in a text editor</li>";
    echo "<li>Search for 'extension=gd' or ';extension=gd'</li>";
    echo "<li>If you find ';extension=gd', remove the semicolon</li>";
    echo "<li>If you don't find it, add this line: <div class='code'>extension=gd</div></li>";
    echo "<li>Save the file</li>";
    echo "<li>Restart Apache</li>";
    echo "</ol>";
    
    echo "<h3>Method 3: Alternative - Use Simple Upload (No GD Required)</h3>";
    echo "<div class='info'>";
    echo "If you can't enable GD extension, the system will automatically fall back to simple file upload without image processing.";
    echo "This means:<br>";
    echo "• Images will be uploaded as-is (no resizing)<br>";
    echo "• No thumbnail generation<br>";
    echo "• Basic file validation only<br>";
    echo "• Still fully functional for product management";
    echo "</div>";
    
    echo "<p><a href='test_image_upload.php' style='background:#ffc107;color:#212529;padding:10px 20px;text-decoration:none;border-radius:4px;'>Test Simple Upload System</a></p>";
}

echo "<h2>🧪 System Information</h2>";

echo "<div class='info'>";
echo "<strong>PHP Version:</strong> " . PHP_VERSION . "<br>";
echo "<strong>Server:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "<strong>Upload Max Filesize:</strong> " . ini_get('upload_max_filesize') . "<br>";
echo "<strong>Post Max Size:</strong> " . ini_get('post_max_size') . "<br>";
echo "<strong>Memory Limit:</strong> " . ini_get('memory_limit') . "<br>";
echo "</div>";

echo "<h2>📋 Available Extensions</h2>";

$imageExtensions = ['gd', 'imagick', 'fileinfo', 'exif'];
echo "<div class='info'>";
foreach ($imageExtensions as $ext) {
    $status = extension_loaded($ext) ? '✅' : '❌';
    echo "$status $ext<br>";
}
echo "</div>";

echo "<h2>🔄 Quick Actions</h2>";
echo "<div style='margin:20px 0;'>";
echo "<a href='?' style='background:#007bff;color:white;padding:10px 20px;text-decoration:none;border-radius:4px;margin-right:10px;'>Refresh Status</a>";
echo "<a href='test_image_upload.php' style='background:#28a745;color:white;padding:10px 20px;text-decoration:none;border-radius:4px;margin-right:10px;'>Test Upload System</a>";
echo "<a href='supplier/add_product.php' style='background:#ffc107;color:#212529;padding:10px 20px;text-decoration:none;border-radius:4px;'>Add Product</a>";
echo "</div>";

if (!extension_loaded('gd')) {
    echo "<div class='warning'>";
    echo "<strong>💡 Tip:</strong> After enabling GD extension and restarting Apache, ";
    echo "<a href='?' style='color:#856404;text-decoration:underline;'>refresh this page</a> to see the updated status.";
    echo "</div>";
}

echo "</body></html>";
?>
