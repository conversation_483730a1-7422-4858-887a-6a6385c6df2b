<?php
// <PERSON><PERSON><PERSON> to diagnose and fix supplier foreign key issues
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html><head><title>Fix Supplier Foreign Key Issues</title>";
echo "<style>body{font-family:Arial;max-width:800px;margin:20px auto;padding:20px;} .success{background:#e6ffe6;color:green;padding:10px;border-radius:4px;margin:10px 0;} .error{background:#ffe6e6;color:red;padding:10px;border-radius:4px;margin:10px 0;} .info{background:#e6f3ff;color:blue;padding:10px;border-radius:4px;margin:10px 0;} .warning{background:#fff3cd;color:#856404;padding:10px;border-radius:4px;margin:10px 0;} table{width:100%;border-collapse:collapse;margin:20px 0;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background:#f5f5f5;}</style>";
echo "</head><body>";

echo "<h1>🔧 Fix Supplier Foreign Key Issues</h1>";

try {
    require_once 'config/db_connect.php';
    
    if (isset($db_connection_error)) {
        echo "<div class='error'>❌ Database connection failed!</div>";
        exit;
    }
    
    echo "<div class='success'>✅ Database connected successfully!</div>";
    
    // Check current session if available
    session_start();
    if (isset($_SESSION['user_id'])) {
        echo "<div class='info'>📋 Current session user_id: {$_SESSION['user_id']}</div>";
        echo "<div class='info'>📋 Current session role: " . ($_SESSION['role'] ?? 'Not set') . "</div>";
    } else {
        echo "<div class='warning'>⚠️ No active session found</div>";
    }
    
    // Check users table structure
    echo "<h2>👥 Users Table Analysis</h2>";
    
    try {
        $stmt = $conn->query("DESCRIBE users");
        $columns = $stmt->fetchAll();
        
        echo "<table>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error checking users table: " . $e->getMessage() . "</div>";
    }
    
    // Check all users
    echo "<h2>👤 All Users in Database</h2>";
    
    try {
        $stmt = $conn->query("SELECT id, username, fullname, email, role, created_at FROM users ORDER BY id");
        $users = $stmt->fetchAll();
        
        if (empty($users)) {
            echo "<div class='warning'>⚠️ No users found in database!</div>";
            echo "<p><a href='create_test_users.php' style='background:#28a745;color:white;padding:10px 20px;text-decoration:none;border-radius:4px;'>Create Test Users</a></p>";
        } else {
            echo "<table>";
            echo "<tr><th>ID</th><th>Username</th><th>Full Name</th><th>Email</th><th>Role</th><th>Created</th></tr>";
            foreach ($users as $user) {
                $rowClass = '';
                if ($user['role'] === 'supplier') {
                    $rowClass = 'style="background-color:#e6ffe6;"';
                } elseif ($user['role'] === 'retailer') {
                    $rowClass = 'style="background-color:#e6f3ff;"';
                }
                
                echo "<tr $rowClass>";
                echo "<td>{$user['id']}</td>";
                echo "<td>{$user['username']}</td>";
                echo "<td>{$user['fullname']}</td>";
                echo "<td>{$user['email']}</td>";
                echo "<td>{$user['role']}</td>";
                echo "<td>" . date('M j, Y', strtotime($user['created_at'])) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Count by role
            $stmt = $conn->query("SELECT role, COUNT(*) as count FROM users GROUP BY role");
            $roleCounts = $stmt->fetchAll();
            
            echo "<div class='info'>";
            echo "<strong>User Counts by Role:</strong><br>";
            foreach ($roleCounts as $roleCount) {
                echo "• {$roleCount['role']}: {$roleCount['count']} users<br>";
            }
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error checking users: " . $e->getMessage() . "</div>";
    }
    
    // Check products table foreign key constraint
    echo "<h2>📦 Products Table Foreign Key Analysis</h2>";
    
    try {
        // Check foreign key constraints
        $stmt = $conn->query("
            SELECT 
                CONSTRAINT_NAME,
                COLUMN_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'products' 
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");
        $constraints = $stmt->fetchAll();
        
        if (empty($constraints)) {
            echo "<div class='warning'>⚠️ No foreign key constraints found on products table</div>";
        } else {
            echo "<table>";
            echo "<tr><th>Constraint Name</th><th>Column</th><th>References Table</th><th>References Column</th></tr>";
            foreach ($constraints as $constraint) {
                echo "<tr>";
                echo "<td>{$constraint['CONSTRAINT_NAME']}</td>";
                echo "<td>{$constraint['COLUMN_NAME']}</td>";
                echo "<td>{$constraint['REFERENCED_TABLE_NAME']}</td>";
                echo "<td>{$constraint['REFERENCED_COLUMN_NAME']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Check for orphaned products (products with invalid supplier_id)
        $stmt = $conn->query("
            SELECT p.id, p.name, p.supplier_id 
            FROM products p 
            LEFT JOIN users u ON p.supplier_id = u.id 
            WHERE u.id IS NULL
        ");
        $orphanedProducts = $stmt->fetchAll();
        
        if (!empty($orphanedProducts)) {
            echo "<div class='error'>❌ Found orphaned products (invalid supplier_id):</div>";
            echo "<table>";
            echo "<tr><th>Product ID</th><th>Product Name</th><th>Invalid Supplier ID</th></tr>";
            foreach ($orphanedProducts as $product) {
                echo "<tr>";
                echo "<td>{$product['id']}</td>";
                echo "<td>{$product['name']}</td>";
                echo "<td>{$product['supplier_id']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<div class='success'>✅ No orphaned products found</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error checking foreign keys: " . $e->getMessage() . "</div>";
    }
    
    // Check current session user validity
    if (isset($_SESSION['user_id'])) {
        echo "<h2>🔍 Session User Validation</h2>";
        
        try {
            $stmt = $conn->prepare("SELECT id, username, fullname, role FROM users WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $sessionUser = $stmt->fetch();
            
            if ($sessionUser) {
                echo "<div class='success'>✅ Session user exists in database</div>";
                echo "<table>";
                echo "<tr><th>Property</th><th>Value</th></tr>";
                echo "<tr><td>ID</td><td>{$sessionUser['id']}</td></tr>";
                echo "<tr><td>Username</td><td>{$sessionUser['username']}</td></tr>";
                echo "<tr><td>Full Name</td><td>{$sessionUser['fullname']}</td></tr>";
                echo "<tr><td>Role</td><td>{$sessionUser['role']}</td></tr>";
                echo "</table>";
                
                if ($sessionUser['role'] === 'supplier') {
                    echo "<div class='success'>✅ User has supplier role - can add products</div>";
                } else {
                    echo "<div class='warning'>⚠️ User does not have supplier role - cannot add products</div>";
                }
            } else {
                echo "<div class='error'>❌ Session user ID {$_SESSION['user_id']} does not exist in database!</div>";
                echo "<div class='warning'>This is the cause of the foreign key constraint error.</div>";
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ Error validating session user: " . $e->getMessage() . "</div>";
        }
    }
    
    // Provide fix options
    echo "<h2>🛠️ Fix Options</h2>";
    
    if (isset($_SESSION['user_id'])) {
        $stmt = $conn->prepare("SELECT id FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        if (!$stmt->fetch()) {
            echo "<div class='warning'>";
            echo "<strong>Problem:</strong> Your session user ID doesn't exist in the database.<br>";
            echo "<strong>Solutions:</strong><br>";
            echo "1. <a href='logout.php'>Logout</a> and login again with valid credentials<br>";
            echo "2. <a href='create_test_users.php'>Create test users</a> if none exist<br>";
            echo "3. Clear your browser session data";
            echo "</div>";
        }
    } else {
        echo "<div class='info'>";
        echo "<strong>No active session.</strong> Please <a href='login.php'>login</a> with a supplier account to add products.";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
}

echo "<h2>🎯 Quick Actions</h2>";
echo "<div style='margin:20px 0;'>";
echo "<a href='create_test_users.php' style='background:#28a745;color:white;padding:10px 20px;text-decoration:none;border-radius:4px;margin-right:10px;'>Create Test Users</a>";
echo "<a href='login.php' style='background:#007bff;color:white;padding:10px 20px;text-decoration:none;border-radius:4px;margin-right:10px;'>Login Page</a>";
echo "<a href='logout.php' style='background:#dc3545;color:white;padding:10px 20px;text-decoration:none;border-radius:4px;margin-right:10px;'>Logout</a>";
echo "<a href='supplier/add_product.php' style='background:#ffc107;color:#212529;padding:10px 20px;text-decoration:none;border-radius:4px;'>Add Product</a>";
echo "</div>";

echo "</body></html>";
?>
