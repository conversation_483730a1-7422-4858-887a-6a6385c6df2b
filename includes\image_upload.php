<?php
/**
 * Image Upload Helper Functions
 * Handles product image uploads with validation and resizing
 */

class ImageUpload {
    private $uploadDir = 'uploads/products/';
    private $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    private $maxFileSize = 5 * 1024 * 1024; // 5MB
    private $maxWidth = 1200;
    private $maxHeight = 1200;
    private $thumbnailWidth = 300;
    private $thumbnailHeight = 300;

    public function __construct() {
        // Create upload directories if they don't exist
        $this->createDirectories();
    }

    private function createDirectories() {
        $dirs = [
            $this->uploadDir,
            $this->uploadDir . 'thumbnails/',
            $this->uploadDir . 'original/'
        ];

        foreach ($dirs as $dir) {
            if (!file_exists($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }

    public function uploadImage($file, $productSku = null) {
        $result = [
            'success' => false,
            'message' => '',
            'filename' => '',
            'thumbnail' => '',
            'original' => ''
        ];

        // Validate file
        $validation = $this->validateFile($file);
        if (!$validation['valid']) {
            $result['message'] = $validation['message'];
            return $result;
        }

        // Generate unique filename
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        $filename = $this->generateFilename($productSku, $extension);

        try {
            // Move uploaded file to temporary location
            $tempPath = $this->uploadDir . 'temp_' . $filename;
            if (!move_uploaded_file($file['tmp_name'], $tempPath)) {
                $result['message'] = 'Failed to upload file';
                return $result;
            }

            // Process and resize images
            $originalPath = $this->uploadDir . 'original/' . $filename;
            $mainPath = $this->uploadDir . $filename;
            $thumbnailPath = $this->uploadDir . 'thumbnails/' . $filename;

            // Copy original
            copy($tempPath, $originalPath);

            // Create main image (resized if needed)
            $this->resizeImage($tempPath, $mainPath, $this->maxWidth, $this->maxHeight);

            // Create thumbnail
            $this->resizeImage($tempPath, $thumbnailPath, $this->thumbnailWidth, $this->thumbnailHeight, true);

            // Clean up temp file
            unlink($tempPath);

            $result['success'] = true;
            $result['message'] = 'Image uploaded successfully';
            $result['filename'] = $filename;
            $result['thumbnail'] = 'thumbnails/' . $filename;
            $result['original'] = 'original/' . $filename;

        } catch (Exception $e) {
            $result['message'] = 'Error processing image: ' . $e->getMessage();
            // Clean up any partial files
            $this->cleanupFiles([$tempPath, $originalPath, $mainPath, $thumbnailPath]);
        }

        return $result;
    }

    private function validateFile($file) {
        $result = ['valid' => false, 'message' => ''];

        // Check if file was uploaded
        if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
            $result['message'] = 'No file uploaded';
            return $result;
        }

        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $result['message'] = $this->getUploadErrorMessage($file['error']);
            return $result;
        }

        // Check file size
        if ($file['size'] > $this->maxFileSize) {
            $result['message'] = 'File size too large. Maximum size is ' . ($this->maxFileSize / 1024 / 1024) . 'MB';
            return $result;
        }

        // Check file type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if (!in_array($mimeType, $this->allowedTypes)) {
            $result['message'] = 'Invalid file type. Allowed types: JPG, PNG, GIF, WebP';
            return $result;
        }

        // Check if it's actually an image
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            $result['message'] = 'File is not a valid image';
            return $result;
        }

        $result['valid'] = true;
        return $result;
    }

    private function generateFilename($productSku, $extension) {
        $timestamp = time();
        $random = mt_rand(1000, 9999);
        
        if ($productSku) {
            $sku = preg_replace('/[^a-zA-Z0-9]/', '', $productSku);
            return $sku . '_' . $timestamp . '_' . $random . '.' . $extension;
        }
        
        return 'product_' . $timestamp . '_' . $random . '.' . $extension;
    }

    private function resizeImage($sourcePath, $destPath, $maxWidth, $maxHeight, $crop = false) {
        $imageInfo = getimagesize($sourcePath);
        $sourceWidth = $imageInfo[0];
        $sourceHeight = $imageInfo[1];
        $mimeType = $imageInfo['mime'];

        // Create source image resource
        switch ($mimeType) {
            case 'image/jpeg':
                $sourceImage = imagecreatefromjpeg($sourcePath);
                break;
            case 'image/png':
                $sourceImage = imagecreatefrompng($sourcePath);
                break;
            case 'image/gif':
                $sourceImage = imagecreatefromgif($sourcePath);
                break;
            case 'image/webp':
                $sourceImage = imagecreatefromwebp($sourcePath);
                break;
            default:
                throw new Exception('Unsupported image type');
        }

        if ($crop) {
            // Calculate crop dimensions for square thumbnail
            $size = min($sourceWidth, $sourceHeight);
            $x = ($sourceWidth - $size) / 2;
            $y = ($sourceHeight - $size) / 2;
            
            $destWidth = $destHeight = min($maxWidth, $maxHeight);
            $destImage = imagecreatetruecolor($destWidth, $destHeight);
            
            // Preserve transparency for PNG and GIF
            if ($mimeType == 'image/png' || $mimeType == 'image/gif') {
                imagealphablending($destImage, false);
                imagesavealpha($destImage, true);
                $transparent = imagecolorallocatealpha($destImage, 255, 255, 255, 127);
                imagefill($destImage, 0, 0, $transparent);
            }
            
            imagecopyresampled($destImage, $sourceImage, 0, 0, $x, $y, $destWidth, $destHeight, $size, $size);
        } else {
            // Calculate proportional resize
            $ratio = min($maxWidth / $sourceWidth, $maxHeight / $sourceHeight);
            
            if ($ratio >= 1) {
                // Don't upscale, just copy
                copy($sourcePath, $destPath);
                imagedestroy($sourceImage);
                return;
            }
            
            $destWidth = round($sourceWidth * $ratio);
            $destHeight = round($sourceHeight * $ratio);
            
            $destImage = imagecreatetruecolor($destWidth, $destHeight);
            
            // Preserve transparency for PNG and GIF
            if ($mimeType == 'image/png' || $mimeType == 'image/gif') {
                imagealphablending($destImage, false);
                imagesavealpha($destImage, true);
                $transparent = imagecolorallocatealpha($destImage, 255, 255, 255, 127);
                imagefill($destImage, 0, 0, $transparent);
            }
            
            imagecopyresampled($destImage, $sourceImage, 0, 0, 0, 0, $destWidth, $destHeight, $sourceWidth, $sourceHeight);
        }

        // Save the resized image
        switch ($mimeType) {
            case 'image/jpeg':
                imagejpeg($destImage, $destPath, 90);
                break;
            case 'image/png':
                imagepng($destImage, $destPath, 9);
                break;
            case 'image/gif':
                imagegif($destImage, $destPath);
                break;
            case 'image/webp':
                imagewebp($destImage, $destPath, 90);
                break;
        }

        imagedestroy($sourceImage);
        imagedestroy($destImage);
    }

    private function getUploadErrorMessage($errorCode) {
        switch ($errorCode) {
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                return 'File size exceeds maximum allowed size';
            case UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'File upload stopped by extension';
            default:
                return 'Unknown upload error';
        }
    }

    private function cleanupFiles($files) {
        foreach ($files as $file) {
            if (file_exists($file)) {
                unlink($file);
            }
        }
    }

    public function deleteImage($filename) {
        if (empty($filename)) return;

        $files = [
            $this->uploadDir . $filename,
            $this->uploadDir . 'thumbnails/' . $filename,
            $this->uploadDir . 'original/' . $filename
        ];

        $this->cleanupFiles($files);
    }

    public function getImageUrl($filename, $type = 'main') {
        if (empty($filename)) return null;

        $baseUrl = $this->getBaseUrl();
        
        switch ($type) {
            case 'thumbnail':
                return $baseUrl . $this->uploadDir . 'thumbnails/' . $filename;
            case 'original':
                return $baseUrl . $this->uploadDir . 'original/' . $filename;
            default:
                return $baseUrl . $this->uploadDir . $filename;
        }
    }

    private function getBaseUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $path = dirname($_SERVER['SCRIPT_NAME']);
        return $protocol . '://' . $host . $path . '/';
    }
}
?>
