<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - PiGit</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>tailwind.config={theme:{extend:{colors:{primary:'#f97316',secondary:'#84cc16'},borderRadius:{'button':'8px'}}}}</script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .hero-bg {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 50%, #dc2626 100%);
            position: relative;
            overflow: hidden;
        }
        .hero-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }
        .input-field:focus {
            border-color: #f97316;
            box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
        }
        .role-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .role-farmer { background: #fef3c7; color: #92400e; }
        .role-supplier { background: #dbeafe; color: #1e40af; }
        .role-retailer { background: #dcfce7; color: #166534; }
    </style>
</head>
<body class="bg-gray-50">
    <?php
    session_start();
    
    // Handle login form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
        require_once 'config/db_connect.php';
        
        $username = trim($_POST['username']);
        $password = $_POST['password'];
        $remember = isset($_POST['remember']);
        
        $errors = [];
        
        if (empty($username)) {
            $errors['username'] = "Username is required";
        }
        if (empty($password)) {
            $errors['password'] = "Password is required";
        }
        
        if (empty($errors) && !isset($db_connection_error)) {
            try {
                // Check user credentials
                $stmt = $conn->prepare("SELECT id, fullname, username, email, password, role, company_name, business_type, phone, address, created_at FROM users WHERE username = :username OR email = :username");
                $stmt->bindParam(':username', $username);
                $stmt->execute();
                
                $user = $stmt->fetch();
                
                if ($user && password_verify($password, $user['password'])) {
                    // Login successful
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['fullname'] = $user['fullname'];
                    $_SESSION['email'] = $user['email'];
                    $_SESSION['role'] = $user['role'];
                    $_SESSION['company_name'] = $user['company_name'];
                    $_SESSION['business_type'] = $user['business_type'];
                    $_SESSION['phone'] = $user['phone'];
                    $_SESSION['address'] = $user['address'];
                    $_SESSION['login_time'] = time();
                    
                    // Set remember me cookie if requested
                    if ($remember) {
                        $token = bin2hex(random_bytes(32));
                        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 days
                        // In production, store this token in database for security
                    }
                    
                    // Redirect based on role
                    switch ($user['role']) {
                        case 'farmer':
                            header("Location: dashboard/farmer_dashboard.php");
                            break;
                        case 'supplier':
                            header("Location: supplier/dashboard.php");
                            break;
                        case 'retailer':
                            header("Location: dashboard/retailer_dashboard.php");
                            break;
                        default:
                            header("Location: dashboard/dashboard.php");
                    }
                    exit();
                } else {
                    $errors['login'] = "Invalid username or password";
                }
            } catch (Exception $e) {
                $errors['db'] = "Login error: " . $e->getMessage();
            }
        }
        
        if (!empty($errors)) {
            $_SESSION['login_errors'] = $errors;
            $_SESSION['login_data'] = ['username' => $username, 'remember' => $remember];
        }
    }
    
    // Handle registration form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['register'])) {
        require_once 'config/db_connect.php';
        
        $fullname = trim($_POST['fullName']);
        $email = trim($_POST['email']);
        $username = trim($_POST['username']);
        $password = $_POST['password'];
        $confirm_password = $_POST['confirmPassword'];
        $role = $_POST['role'];
        $terms = isset($_POST['terms']);
        
        $errors = [];
        
        // Basic validation
        if (empty($fullname)) $errors['fullname'] = "Full name is required";
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) $errors['email'] = "Valid email is required";
        if (empty($username) || strlen($username) < 3) $errors['username'] = "Username must be at least 3 characters";
        if (empty($password) || strlen($password) < 8) $errors['password'] = "Password must be at least 8 characters";
        if ($password !== $confirm_password) $errors['confirm_password'] = "Passwords do not match";
        if (empty($role) || !in_array($role, ['farmer', 'supplier', 'retailer'])) $errors['role'] = "Please select a valid role";
        if (!$terms) $errors['terms'] = "You must agree to the terms";
        
        if (empty($errors) && !isset($db_connection_error)) {
            try {
                // Check if username exists
                $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE username = :username");
                $stmt->bindParam(':username', $username);
                $stmt->execute();
                
                if ($stmt->fetchColumn() > 0) {
                    $errors['username'] = "Username already exists";
                } else {
                    // Check if email exists
                    $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE email = :email");
                    $stmt->bindParam(':email', $email);
                    $stmt->execute();
                    
                    if ($stmt->fetchColumn() > 0) {
                        $errors['email'] = "Email already exists";
                    } else {
                        // Insert user
                        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                        
                        $sql = "INSERT INTO users (fullname, username, email, password, role) VALUES (:fullname, :username, :email, :password, :role)";
                        $stmt = $conn->prepare($sql);
                        $stmt->bindParam(':fullname', $fullname);
                        $stmt->bindParam(':username', $username);
                        $stmt->bindParam(':email', $email);
                        $stmt->bindParam(':password', $hashed_password);
                        $stmt->bindParam(':role', $role);
                        
                        if ($stmt->execute()) {
                            $_SESSION['success'] = "Registration successful! Please login with your credentials.";
                            header("Location: login.php");
                            exit();
                        } else {
                            $errors['db'] = "Registration failed. Please try again.";
                        }
                    }
                }
            } catch (Exception $e) {
                $errors['db'] = "Database error: " . $e->getMessage();
            }
        }
        
        if (!empty($errors)) {
            $_SESSION['reg_errors'] = $errors;
            $_SESSION['reg_data'] = $_POST;
        }
    }
    
    // Get session data
    $login_errors = $_SESSION['login_errors'] ?? [];
    $login_data = $_SESSION['login_data'] ?? [];
    $reg_errors = $_SESSION['reg_errors'] ?? [];
    $reg_data = $_SESSION['reg_data'] ?? [];
    $success_message = $_SESSION['success'] ?? '';
    
    // Clear session data
    unset($_SESSION['login_errors'], $_SESSION['login_data'], $_SESSION['reg_errors'], $_SESSION['reg_data'], $_SESSION['success']);
    
    // Determine which form to show
    $show_register = isset($_GET['action']) && $_GET['action'] === 'register';
    ?>

    <div class="min-h-screen flex">
        <!-- Left Side - Hero Section -->
        <div class="hidden lg:flex lg:w-1/2 hero-bg items-center justify-center relative">
            <div class="text-center text-white z-10 px-8">
                <div class="mb-8">
                    <h1 class="font-['Pacifico'] text-5xl mb-4">PiGit</h1>
                    <p class="text-xl opacity-90">Smart Farm Management Platform</p>
                </div>
                
                <div class="space-y-6 text-left max-w-md">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                            <i class="ri-plant-line ri-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold">For Farmers</h3>
                            <p class="text-sm opacity-80">Manage livestock, track feed, monitor health</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                            <i class="ri-truck-line ri-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold">For Suppliers</h3>
                            <p class="text-sm opacity-80">Supply feed, equipment, and materials</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                            <i class="ri-store-line ri-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold">For Retailers</h3>
                            <p class="text-sm opacity-80">Source and sell farm products</p>
                        </div>
                    </div>
                </div>
                
                <div class="mt-12 text-center">
                    <p class="text-sm opacity-75">"PiGit has transformed how we manage our farm operations"</p>
                    <p class="text-xs opacity-60 mt-2">- Happy Farmer</p>
                </div>
            </div>
        </div>

        <!-- Right Side - Forms -->
        <div class="w-full lg:w-1/2 flex items-center justify-center p-8">
            <div class="w-full max-w-md space-y-8">
                <!-- Logo for mobile -->
                <div class="text-center lg:hidden">
                    <h1 class="font-['Pacifico'] text-3xl text-primary mb-2">PiGit</h1>
                    <p class="text-gray-600">Smart Farm Management</p>
                </div>

                <!-- Success Message -->
                <?php if ($success_message): ?>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex">
                            <i class="ri-check-circle-line text-green-400 ri-lg"></i>
                            <div class="ml-3">
                                <p class="text-sm text-green-800"><?php echo htmlspecialchars($success_message); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Form Toggle -->
                <div class="flex bg-gray-100 rounded-lg p-1">
                    <button type="button" onclick="showLogin()" id="loginTab" 
                            class="flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors <?php echo !$show_register ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-500 hover:text-gray-700'; ?>">
                        Login
                    </button>
                    <button type="button" onclick="showRegister()" id="registerTab" 
                            class="flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors <?php echo $show_register ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-500 hover:text-gray-700'; ?>">
                        Register
                    </button>
                </div>

                <!-- Login Form -->
                <div id="loginForm" class="<?php echo $show_register ? 'hidden' : ''; ?>">
                    <div class="text-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-900">Welcome Back</h2>
                        <p class="text-gray-600">Sign in to your account</p>
                    </div>

                    <?php if (!empty($login_errors)): ?>
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                            <div class="flex">
                                <i class="ri-error-warning-line text-red-400 ri-lg"></i>
                                <div class="ml-3">
                                    <ul class="text-sm text-red-700">
                                        <?php foreach ($login_errors as $error): ?>
                                            <li><?php echo htmlspecialchars($error); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <form method="POST" class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Username or Email</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="ri-user-line text-gray-400"></i>
                                </div>
                                <input type="text" name="username" value="<?php echo htmlspecialchars($login_data['username'] ?? ''); ?>" 
                                       class="input-field w-full pl-10 pr-3 py-3 border border-gray-300 rounded-button focus:outline-none" 
                                       placeholder="Enter username or email" required>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="ri-lock-line text-gray-400"></i>
                                </div>
                                <input type="password" name="password" 
                                       class="input-field w-full pl-10 pr-10 py-3 border border-gray-300 rounded-button focus:outline-none" 
                                       placeholder="Enter your password" required>
                                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePassword(this)">
                                    <i class="ri-eye-off-line text-gray-400"></i>
                                </button>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <label class="flex items-center">
                                <input type="checkbox" name="remember" class="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary" <?php echo ($login_data['remember'] ?? false) ? 'checked' : ''; ?>>
                                <span class="ml-2 text-sm text-gray-600">Remember me</span>
                            </label>
                            <a href="#" class="text-sm text-primary hover:underline">Forgot password?</a>
                        </div>

                        <button type="submit" name="login" class="w-full bg-primary text-white py-3 px-4 rounded-button hover:bg-primary/90 transition-colors duration-300 font-medium">
                            Sign In
                        </button>
                    </form>
                </div>

                <!-- Register Form -->
                <div id="registerForm" class="<?php echo !$show_register ? 'hidden' : ''; ?>">
                    <div class="text-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-900">Create Account</h2>
                        <p class="text-gray-600">Join the PiGit community</p>
                    </div>

                    <?php if (!empty($reg_errors)): ?>
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                            <div class="flex">
                                <i class="ri-error-warning-line text-red-400 ri-lg"></i>
                                <div class="ml-3">
                                    <ul class="text-sm text-red-700 list-disc list-inside">
                                        <?php foreach ($reg_errors as $error): ?>
                                            <li><?php echo htmlspecialchars($error); ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <form method="POST" class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                            <input type="text" name="fullName" value="<?php echo htmlspecialchars($reg_data['fullName'] ?? ''); ?>" 
                                   class="input-field w-full px-3 py-3 border border-gray-300 rounded-button focus:outline-none" 
                                   placeholder="Enter your full name" required>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                            <input type="email" name="email" value="<?php echo htmlspecialchars($reg_data['email'] ?? ''); ?>" 
                                   class="input-field w-full px-3 py-3 border border-gray-300 rounded-button focus:outline-none" 
                                   placeholder="<EMAIL>" required>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                            <input type="text" name="username" value="<?php echo htmlspecialchars($reg_data['username'] ?? ''); ?>" 
                                   class="input-field w-full px-3 py-3 border border-gray-300 rounded-button focus:outline-none" 
                                   placeholder="Choose a username" required>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                            <select name="role" class="input-field w-full px-3 py-3 border border-gray-300 rounded-button focus:outline-none" required>
                                <option value="">Select your role</option>
                                <option value="farmer" <?php echo ($reg_data['role'] ?? '') === 'farmer' ? 'selected' : ''; ?>>Farmer</option>
                                <option value="supplier" <?php echo ($reg_data['role'] ?? '') === 'supplier' ? 'selected' : ''; ?>>Supplier</option>
                                <option value="retailer" <?php echo ($reg_data['role'] ?? '') === 'retailer' ? 'selected' : ''; ?>>Retailer</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                            <input type="password" name="password" 
                                   class="input-field w-full px-3 py-3 border border-gray-300 rounded-button focus:outline-none" 
                                   placeholder="Minimum 8 characters" required>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Confirm Password</label>
                            <input type="password" name="confirmPassword" 
                                   class="input-field w-full px-3 py-3 border border-gray-300 rounded-button focus:outline-none" 
                                   placeholder="Confirm your password" required>
                        </div>

                        <div class="flex items-start">
                            <input type="checkbox" name="terms" class="mt-1 mr-3 h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary" required>
                            <label class="text-sm text-gray-600">
                                I agree to the <a href="#" class="text-primary hover:underline">Terms of Service</a> and 
                                <a href="#" class="text-primary hover:underline">Privacy Policy</a>
                            </label>
                        </div>

                        <button type="submit" name="register" class="w-full bg-primary text-white py-3 px-4 rounded-button hover:bg-primary/90 transition-colors duration-300 font-medium">
                            Create Account
                        </button>
                    </form>

                    <div class="mt-6 text-center">
                        <p class="text-sm text-gray-600">
                            Need a business account? 
                            <a href="register.php" class="text-primary hover:underline font-medium">Register as Supplier/Retailer</a>
                        </p>
                    </div>
                </div>

                <!-- Social Login -->
                <div class="mt-6">
                    <div class="relative flex items-center justify-center">
                        <div class="border-t border-gray-300 absolute w-full"></div>
                        <div class="bg-white px-4 relative z-10 text-sm text-gray-500">or continue with</div>
                    </div>

                    <div class="grid grid-cols-3 gap-3 mt-4">
                        <button class="flex items-center justify-center py-2 px-4 border border-gray-300 rounded-button hover:bg-gray-50">
                            <i class="ri-google-fill text-[#EA4335] ri-lg"></i>
                        </button>
                        <button class="flex items-center justify-center py-2 px-4 border border-gray-300 rounded-button hover:bg-gray-50">
                            <i class="ri-facebook-fill text-[#1877F2] ri-lg"></i>
                        </button>
                        <button class="flex items-center justify-center py-2 px-4 border border-gray-300 rounded-button hover:bg-gray-50">
                            <i class="ri-apple-fill text-[#000000] ri-lg"></i>
                        </button>
                    </div>
                </div>

                <!-- Footer -->
                <div class="text-center text-sm text-gray-500">
                    <a href="index.php" class="hover:text-primary">← Back to Home</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showLogin() {
            document.getElementById('loginForm').classList.remove('hidden');
            document.getElementById('registerForm').classList.add('hidden');
            document.getElementById('loginTab').classList.add('bg-white', 'text-gray-900', 'shadow-sm');
            document.getElementById('loginTab').classList.remove('text-gray-500');
            document.getElementById('registerTab').classList.remove('bg-white', 'text-gray-900', 'shadow-sm');
            document.getElementById('registerTab').classList.add('text-gray-500');
            
            // Update URL without reload
            history.pushState({}, '', 'login.php');
        }

        function showRegister() {
            document.getElementById('registerForm').classList.remove('hidden');
            document.getElementById('loginForm').classList.add('hidden');
            document.getElementById('registerTab').classList.add('bg-white', 'text-gray-900', 'shadow-sm');
            document.getElementById('registerTab').classList.remove('text-gray-500');
            document.getElementById('loginTab').classList.remove('bg-white', 'text-gray-900', 'shadow-sm');
            document.getElementById('loginTab').classList.add('text-gray-500');
            
            // Update URL without reload
            history.pushState({}, '', 'login.php?action=register');
        }

        function togglePassword(button) {
            const input = button.previousElementSibling;
            const icon = button.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'ri-eye-line text-gray-400';
            } else {
                input.type = 'password';
                icon.className = 'ri-eye-off-line text-gray-400';
            }
        }

        // Initialize form based on URL parameter
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('action') === 'register') {
                showRegister();
            }
        });
    </script>
</body>
</html>
