<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - PiGit</title>
     <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>tailwind.config={theme:{extend:{colors:{primary:'#f97316',secondary:'#84cc16'},borderRadius:{'button':'8px'}}}}</script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .hero-bg {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 50%, #dc2626 100%);
            position: relative;
            overflow: hidden;
        }
        .hero-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }
        .input-field:focus {
            border-color: #f97316;
            box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
        }
        .role-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .role-farmer { background: #fef3c7; color: #92400e; }
        .role-supplier { background: #dbeafe; color: #1e40af; }
        .role-retailer { background: #dcfce7; color: #166534; }
    </style>
</head>
<body class="bg-gray-50">
    <?php
    session_start();
    
    // Handle form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        require_once 'config/db_connect.php';
        
        // Get form data
        $fullname = trim($_POST['fullName']);
        $email = trim($_POST['email']);
        $username = trim($_POST['username']);
        $password = $_POST['password'];
        $confirm_password = $_POST['confirmPassword'];
        $role = $_POST['role'];
        $company_name = trim($_POST['companyName']);
        $business_type = $_POST['businessType'];
        $phone = trim($_POST['phone']);
        $address = trim($_POST['address']);
        $terms = isset($_POST['terms']);
        
        $errors = [];
        
        // Validation
        if (empty($fullname)) $errors['fullname'] = "Full name is required";
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) $errors['email'] = "Valid email is required";
        if (empty($username) || strlen($username) < 3) $errors['username'] = "Username must be at least 3 characters";
        if (empty($password) || strlen($password) < 8) $errors['password'] = "Password must be at least 8 characters";
        if ($password !== $confirm_password) $errors['confirm_password'] = "Passwords do not match";
        if (!in_array($role, ['supplier', 'retailer'])) $errors['role'] = "Please select either Supplier or Retailer";
        if (empty($company_name)) $errors['company_name'] = "Company name is required";
        if (empty($business_type)) $errors['business_type'] = "Business type is required";
        if (empty($phone)) $errors['phone'] = "Phone number is required";
        if (empty($address)) $errors['address'] = "Address is required";
        if (!$terms) $errors['terms'] = "You must agree to the terms";
        
        if (empty($errors) && !isset($db_connection_error)) {
            try {
                // Check if username exists
                $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE username = :username");
                $stmt->bindParam(':username', $username);
                $stmt->execute();
                
                if ($stmt->fetchColumn() > 0) {
                    $errors['username'] = "Username already exists";
                } else {
                    // Check if email exists
                    $stmt = $conn->prepare("SELECT COUNT(*) FROM users WHERE email = :email");
                    $stmt->bindParam(':email', $email);
                    $stmt->execute();
                    
                    if ($stmt->fetchColumn() > 0) {
                        $errors['email'] = "Email already exists";
                    } else {
                        // Insert user
                        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                        
                        $sql = "INSERT INTO users (fullname, username, email, password, role, company_name, business_type, phone, address) 
                                VALUES (:fullname, :username, :email, :password, :role, :company_name, :business_type, :phone, :address)";
                        
                        $stmt = $conn->prepare($sql);
                        $stmt->bindParam(':fullname', $fullname);
                        $stmt->bindParam(':username', $username);
                        $stmt->bindParam(':email', $email);
                        $stmt->bindParam(':password', $hashed_password);
                        $stmt->bindParam(':role', $role);
                        $stmt->bindParam(':company_name', $company_name);
                        $stmt->bindParam(':business_type', $business_type);
                        $stmt->bindParam(':phone', $phone);
                        $stmt->bindParam(':address', $address);
                        
                        if ($stmt->execute()) {
                            $_SESSION['success'] = "Registration successful! Welcome to PiGit as a " . ucfirst($role) . "!";
                            header("Location: login.php");
                            exit();
                        } else {
                            $errors['db'] = "Registration failed. Please try again.";
                        }
                    }
                }
            } catch (Exception $e) {
                $errors['db'] = "Database error: " . $e->getMessage();
            }
        }
        
        if (!empty($errors)) {
            $_SESSION['reg_errors'] = $errors;
            $_SESSION['reg_data'] = $_POST;
        }
    }
    
    $reg_errors = $_SESSION['reg_errors'] ?? [];
    $reg_data = $_SESSION['reg_data'] ?? [];
    unset($_SESSION['reg_errors'], $_SESSION['reg_data']);
    ?>

    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="index.php" class="font-['Pacifico'] text-2xl text-primary">PiGit</a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="login.php" class="text-gray-600 hover:text-gray-900">Already have an account?</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-4xl w-full space-y-8">
            <!-- Header -->
            <div class="text-center">
                <h1 class="text-4xl font-bold text-gray-900 mb-2">Join PiGit Business Network</h1>
                <p class="text-lg text-gray-600">Register as a Supplier or Retailer to connect with farmers and grow your business</p>
            </div>

            <!-- Error Messages -->
            <?php if (!empty($reg_errors)): ?>
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="flex">
                        <i class="ri-error-warning-line text-red-400 ri-lg"></i>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                            <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                                <?php foreach ($reg_errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <form method="POST" class="bg-white shadow-xl rounded-lg p-8 space-y-8">
                <!-- Role Selection -->
                <div>
                    <h2 class="text-2xl font-semibold text-gray-900 mb-6">Choose Your Business Role</h2>
                    <div class="grid md:grid-cols-2 gap-6">
                        <!-- Supplier Card -->
                        <div class="role-card border-2 border-gray-200 rounded-lg p-6 <?php echo ($reg_data['role'] ?? '') === 'supplier' ? 'selected' : ''; ?>" onclick="selectRole('supplier')">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="ri-truck-line text-blue-600 ri-2x"></i>
                                </div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-2">Supplier</h3>
                                <p class="text-gray-600 mb-4">Supply feed, equipment, and farming materials</p>
                                <ul class="text-sm text-gray-500 space-y-1 text-left">
                                    <li>• Manage product catalog</li>
                                    <li>• Track orders and deliveries</li>
                                    <li>• Connect with farmers directly</li>
                                    <li>• Business analytics</li>
                                </ul>
                            </div>
                            <input type="radio" name="role" value="supplier" class="hidden" <?php echo ($reg_data['role'] ?? '') === 'supplier' ? 'checked' : ''; ?>>
                        </div>

                        <!-- Retailer Card -->
                        <div class="role-card border-2 border-gray-200 rounded-lg p-6 <?php echo ($reg_data['role'] ?? '') === 'retailer' ? 'selected' : ''; ?>" onclick="selectRole('retailer')">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="ri-store-line text-green-600 ri-2x"></i>
                                </div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-2">Retailer</h3>
                                <p class="text-gray-600 mb-4">Sell farm products to consumers</p>
                                <ul class="text-sm text-gray-500 space-y-1 text-left">
                                    <li>• Manage inventory</li>
                                    <li>• Source from farmers</li>
                                    <li>• Customer management</li>
                                    <li>• Sales analytics</li>
                                </ul>
                            </div>
                            <input type="radio" name="role" value="retailer" class="hidden" <?php echo ($reg_data['role'] ?? '') === 'retailer' ? 'checked' : ''; ?>>
                        </div>
                    </div>
                </div>

                <!-- Personal Information -->
                <div>
                    <h2 class="text-2xl font-semibold text-gray-900 mb-6">Personal Information</h2>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                            <input type="text" name="fullName" value="<?php echo htmlspecialchars($reg_data['fullName'] ?? ''); ?>" 
                                   class="input-field w-full px-4 py-3 border border-gray-300 rounded-button focus:outline-none" 
                                   placeholder="Enter your full name" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                            <input type="email" name="email" value="<?php echo htmlspecialchars($reg_data['email'] ?? ''); ?>" 
                                   class="input-field w-full px-4 py-3 border border-gray-300 rounded-button focus:outline-none" 
                                   placeholder="<EMAIL>" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Username *</label>
                            <input type="text" name="username" value="<?php echo htmlspecialchars($reg_data['username'] ?? ''); ?>" 
                                   class="input-field w-full px-4 py-3 border border-gray-300 rounded-button focus:outline-none" 
                                   placeholder="Choose a username" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                            <input type="tel" name="phone" value="<?php echo htmlspecialchars($reg_data['phone'] ?? ''); ?>" 
                                   class="input-field w-full px-4 py-3 border border-gray-300 rounded-button focus:outline-none" 
                                   placeholder="+****************" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Password *</label>
                            <input type="password" name="password" 
                                   class="input-field w-full px-4 py-3 border border-gray-300 rounded-button focus:outline-none" 
                                   placeholder="Minimum 8 characters" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Confirm Password *</label>
                            <input type="password" name="confirmPassword" 
                                   class="input-field w-full px-4 py-3 border border-gray-300 rounded-button focus:outline-none" 
                                   placeholder="Confirm your password" required>
                        </div>
                    </div>
                </div>

                <!-- Business Information -->
                <div>
                    <h2 class="text-2xl font-semibold text-gray-900 mb-6">Business Information</h2>
                    <div class="space-y-6">
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Company Name *</label>
                                <input type="text" name="companyName" value="<?php echo htmlspecialchars($reg_data['companyName'] ?? ''); ?>" 
                                       class="input-field w-full px-4 py-3 border border-gray-300 rounded-button focus:outline-none" 
                                       placeholder="Your company name" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Business Type *</label>
                                <select name="businessType" class="input-field w-full px-4 py-3 border border-gray-300 rounded-button focus:outline-none" required>
                                    <option value="">Select Business Type</option>
                                    <optgroup label="Supplier Types">
                                        <option value="feed_supplier" <?php echo ($reg_data['businessType'] ?? '') === 'feed_supplier' ? 'selected' : ''; ?>>Feed Supplier</option>
                                        <option value="equipment_supplier" <?php echo ($reg_data['businessType'] ?? '') === 'equipment_supplier' ? 'selected' : ''; ?>>Equipment Supplier</option>
                                        <option value="veterinary_supplier" <?php echo ($reg_data['businessType'] ?? '') === 'veterinary_supplier' ? 'selected' : ''; ?>>Veterinary Supplier</option>
                                        <option value="seed_supplier" <?php echo ($reg_data['businessType'] ?? '') === 'seed_supplier' ? 'selected' : ''; ?>>Seed Supplier</option>
                                    </optgroup>
                                    <optgroup label="Retailer Types">
                                        <option value="grocery_retailer" <?php echo ($reg_data['businessType'] ?? '') === 'grocery_retailer' ? 'selected' : ''; ?>>Grocery Retailer</option>
                                        <option value="restaurant_chain" <?php echo ($reg_data['businessType'] ?? '') === 'restaurant_chain' ? 'selected' : ''; ?>>Restaurant Chain</option>
                                        <option value="meat_processor" <?php echo ($reg_data['businessType'] ?? '') === 'meat_processor' ? 'selected' : ''; ?>>Meat Processor</option>
                                        <option value="food_distributor" <?php echo ($reg_data['businessType'] ?? '') === 'food_distributor' ? 'selected' : ''; ?>>Food Distributor</option>
                                    </optgroup>
                                    <option value="other" <?php echo ($reg_data['businessType'] ?? '') === 'other' ? 'selected' : ''; ?>>Other</option>
                                </select>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Business Address *</label>
                            <textarea name="address" rows="3" 
                                      class="input-field w-full px-4 py-3 border border-gray-300 rounded-button focus:outline-none" 
                                      placeholder="Enter your complete business address" required><?php echo htmlspecialchars($reg_data['address'] ?? ''); ?></textarea>
                        </div>
                        
                        <!-- Terms and Conditions -->
                        <div class="flex items-start">
                            <input type="checkbox" name="terms" class="mt-1 mr-3 h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary" required>
                            <label class="text-sm text-gray-600">
                                I agree to the <a href="#" class="text-primary hover:underline font-medium">Terms of Service</a> and 
                                <a href="#" class="text-primary hover:underline font-medium">Privacy Policy</a>. 
                                I understand that my business information will be used to connect me with farmers and other users on the platform.
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="text-center">
                    <button type="submit" class="w-full md:w-auto bg-primary text-white px-8 py-4 rounded-button hover:bg-primary/90 transition-colors duration-300 font-semibold text-lg">
                        Create Business Account
                    </button>
                </div>
            </form>

            <!-- Footer Links -->
            <div class="text-center space-y-4">
                <p class="text-sm text-gray-600">
                    Already have an account? 
                    <a href="login.php" class="text-primary hover:underline font-medium">Sign in here</a>
                </p>
                <p class="text-sm text-gray-500">
                    Looking to register as a farmer? 
                    <a href="login.php?action=register" class="text-primary hover:underline">Farmer Registration</a>
                </p>
            </div>
        </div>
    </div>

    <script>
        function selectRole(role) {
            // Remove selected class from all cards
            document.querySelectorAll('.role-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Add selected class to clicked card
            event.currentTarget.classList.add('selected');
            
            // Check the radio button
            document.querySelector(`input[value="${role}"]`).checked = true;
        }

        // Initialize role selection if data exists
        document.addEventListener('DOMContentLoaded', function() {
            const selectedRole = document.querySelector('input[name="role"]:checked');
            if (selectedRole) {
                selectRole(selectedRole.value);
            }
        });

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const role = document.querySelector('input[name="role"]:checked');
            if (!role) {
                e.preventDefault();
                alert('Please select your business role (Supplier or Retailer)');
                return false;
            }
            
            const password = document.querySelector('input[name="password"]').value;
            const confirmPassword = document.querySelector('input[name="confirmPassword"]').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Passwords do not match');
                return false;
            }
            
            if (password.length < 8) {
                e.preventDefault();
                alert('Password must be at least 8 characters long');
                return false;
            }
        });
    </script>
</body>
</html>
