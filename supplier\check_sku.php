<?php
// API endpoint to check if a SKU is available
header('Content-Type: application/json');

// Start session and check authentication
session_start();

// Check if user is logged in and has supplier role
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'supplier') {
    http_response_code(403);
    echo json_encode(['available' => false, 'error' => 'Access denied']);
    exit();
}

// Check if SKU is provided
if (!isset($_GET['sku']) || empty($_GET['sku'])) {
    echo json_encode(['available' => false, 'error' => 'SKU is required']);
    exit();
}

$sku = trim($_GET['sku']);
$productId = isset($_GET['product_id']) ? intval($_GET['product_id']) : null;

// Basic SKU validation
if (strlen($sku) < 3) {
    echo json_encode(['available' => false, 'error' => 'SKU too short']);
    exit();
}

if (strlen($sku) > 50) {
    echo json_encode(['available' => false, 'error' => 'SKU too long']);
    exit();
}

// Check for valid characters (alphanumeric, hyphens, underscores)
if (!preg_match('/^[A-Za-z0-9\-_]+$/', $sku)) {
    echo json_encode(['available' => false, 'error' => 'SKU contains invalid characters']);
    exit();
}

try {
    require_once '../config/db_connect.php';
    
    if (isset($db_connection_error)) {
        echo json_encode(['available' => false, 'error' => 'Database connection failed']);
        exit();
    }
    
    // Check if SKU exists (excluding current product if editing)
    if ($productId) {
        // For editing - exclude the current product
        $stmt = $conn->prepare("SELECT COUNT(*) FROM products WHERE sku = ? AND id != ?");
        $stmt->execute([$sku, $productId]);
    } else {
        // For new products - check all products
        $stmt = $conn->prepare("SELECT COUNT(*) FROM products WHERE sku = ?");
        $stmt->execute([$sku]);
    }
    
    $count = $stmt->fetchColumn();
    $available = ($count == 0);
    
    $response = [
        'available' => $available,
        'sku' => $sku
    ];
    
    if (!$available) {
        $response['error'] = 'SKU already exists';
        
        // Get the existing product info
        $stmt = $conn->prepare("SELECT p.name, u.fullname as supplier_name FROM products p JOIN users u ON p.supplier_id = u.id WHERE p.sku = ? LIMIT 1");
        $stmt->execute([$sku]);
        $existingProduct = $stmt->fetch();
        
        if ($existingProduct) {
            $response['existing_product'] = $existingProduct['name'];
            $response['existing_supplier'] = $existingProduct['supplier_name'];
        }
    }
    
    echo json_encode($response);
    
} catch (PDOException $e) {
    echo json_encode(['available' => false, 'error' => 'Database error: ' . $e->getMessage()]);
} catch (Exception $e) {
    echo json_encode(['available' => false, 'error' => 'Error: ' . $e->getMessage()]);
}
?>
