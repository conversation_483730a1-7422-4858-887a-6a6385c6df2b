<?php
// Start session
session_start();

// Check if user is logged in and has supplier role
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'supplier') {
    header("Location: ../login.php");
    exit();
}

// Check if product ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "No product ID provided";
    header("Location: products.php");
    exit();
}

$product_id = $_GET['id'];

// Connect to database
require_once '../config/database.php';

try {
    // Check if product belongs to this supplier
    $stmt = $conn->prepare("SELECT * FROM products WHERE id = ? AND supplier_id = ?");
    $stmt->execute([$product_id, $_SESSION['user_id']]);
    
    if ($stmt->rowCount() === 0) {
        $_SESSION['error'] = "Product not found or you don't have permission to delete it";
        header("Location: products.php");
        exit();
    }
    
    // Delete product
    $stmt = $conn->prepare("DELETE FROM products WHERE id = ?");
    $stmt->execute([$product_id]);
    
    $_SESSION['success'] = "Product deleted successfully";
} catch (PDOException $e) {
    $_SESSION['error'] = "Error deleting product: " . $e->getMessage();
}

// Redirect back to products page
header("Location: products.php");
exit();