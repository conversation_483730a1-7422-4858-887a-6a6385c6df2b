<?php
// Start session
session_start();

// Check if user is logged in and has supplier role
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'supplier') {
    header("Location: ../login.php");
    exit();
}

// Check if product ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "No product ID provided";
    header("Location: products.php");
    exit();
}

$product_id = $_GET['id'];

// Connect to database
require_once '../config/db_connect.php';
require_once '../includes/smart_image_upload.php';

$errors = [];
$product = null;
$imageUpload = new SmartImageUpload();

// Fetch product data
try {
    if (!isset($db_connection_error)) {
        $stmt = $conn->prepare("SELECT * FROM products WHERE id = ? AND supplier_id = ?");
        $stmt->execute([$product_id, $_SESSION['user_id']]);
        $product = $stmt->fetch();

        if (!$product) {
            $_SESSION['error'] = "Product not found or you don't have permission to edit it";
            header("Location: products.php");
            exit();
        }
    }
} catch (PDOException $e) {
    $_SESSION['error'] = "Error fetching product: " . $e->getMessage();
    header("Location: products.php");
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $name = trim($_POST['name']);
    $sku = trim($_POST['sku']);
    $description = trim($_POST['description']);
    $category = $_POST['category'];
    $price = floatval($_POST['price']);
    $stock_quantity = intval($_POST['stock_quantity']);
    $min_stock_level = intval($_POST['min_stock_level']);
    $weight = !empty($_POST['weight']) ? floatval($_POST['weight']) : null;
    $dimensions = trim($_POST['dimensions']);
    $status = $_POST['status'];
    $image_url = $product['image_url']; // Keep existing image by default

    // Handle image upload
    if (isset($_FILES['product_image']) && $_FILES['product_image']['error'] !== UPLOAD_ERR_NO_FILE) {
        $uploadResult = $imageUpload->uploadImage($_FILES['product_image'], $sku);
        if ($uploadResult['success']) {
            // Delete old image if it exists
            if (!empty($product['image_url'])) {
                $imageUpload->deleteImage($product['image_url']);
            }
            $image_url = $uploadResult['filename'];
        } else {
            $errors['image'] = $uploadResult['message'];
        }
    }

    // Validation
    if (empty($name)) $errors['name'] = "Product name is required";
    if (empty($sku)) $errors['sku'] = "SKU is required";
    if (empty($category)) $errors['category'] = "Category is required";
    if ($price <= 0) $errors['price'] = "Price must be greater than 0";
    if ($stock_quantity < 0) $errors['stock_quantity'] = "Stock quantity cannot be negative";
    if ($min_stock_level < 0) $errors['min_stock_level'] = "Minimum stock level cannot be negative";

    // Check if SKU already exists (excluding current product)
    if (empty($errors['sku']) && !isset($db_connection_error)) {
        try {
            $stmt = $conn->prepare("SELECT COUNT(*) FROM products WHERE sku = ? AND id != ?");
            $stmt->execute([$sku, $product_id]);
            if ($stmt->fetchColumn() > 0) {
                $errors['sku'] = "SKU already exists. Please use a unique SKU.";
            }
        } catch (PDOException $e) {
            $errors['db'] = "Database error: " . $e->getMessage();
        }
    }

    // If no errors, update the product
    if (empty($errors) && !isset($db_connection_error)) {
        try {
            // Verify that the current user exists and is a supplier
            $stmt = $conn->prepare("SELECT id, role FROM users WHERE id = ? AND role = 'supplier'");
            $stmt->execute([$_SESSION['user_id']]);
            $user = $stmt->fetch();

            if (!$user) {
                $errors['db'] = "Invalid supplier account. Please contact administrator.";
            } else {
                $sql = "UPDATE products SET name = ?, sku = ?, description = ?, category = ?, price = ?,
                        stock_quantity = ?, min_stock_level = ?, weight = ?, dimensions = ?, status = ?, image_url = ?,
                        updated_at = CURRENT_TIMESTAMP WHERE id = ? AND supplier_id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->execute([
                    $name, $sku, $description, $category, $price, $stock_quantity,
                    $min_stock_level, $weight, $dimensions, $status, $image_url, $product_id, $_SESSION['user_id']
                ]);

                $_SESSION['success'] = "Product '$name' updated successfully!";
                header("Location: products.php");
                exit();
            }

        } catch (PDOException $e) {
            // Provide more specific error messages
            if (strpos($e->getMessage(), 'foreign key constraint') !== false) {
                $errors['db'] = "Database error: Your supplier account is not properly configured. Please contact administrator.";
            } elseif (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                $errors['db'] = "A product with this SKU already exists. Please use a unique SKU.";
            } else {
                $errors['db'] = "Error updating product: " . $e->getMessage();
            }
        }
    }
} else {
    // Pre-fill form with existing data
    $_POST = [
        'name' => $product['name'],
        'sku' => $product['sku'],
        'description' => $product['description'],
        'category' => $product['category'],
        'price' => $product['price'],
        'stock_quantity' => $product['stock_quantity'],
        'min_stock_level' => $product['min_stock_level'],
        'weight' => $product['weight'],
        'dimensions' => $product['dimensions'],
        'status' => $product['status']
    ];
}

$user_name = $_SESSION['fullname'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PiGit - Edit Product</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>tailwind.config={theme:{extend:{colors:{primary:'#f97316',secondary:'#84cc16'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex flex-col">
        <!-- Header/Navigation -->
        <header class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex">
                        <div class="flex-shrink-0 flex items-center">
                            <a href="/" class="font-['Pacifico'] text-2xl text-primary">PiGit</a>
                        </div>
                        <nav class="ml-6 flex space-x-8">
                            <a href="dashboard.php" class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                Dashboard
                            </a>
                            <a href="products.php" class="inline-flex items-center px-1 pt-1 border-b-2 border-primary text-sm font-medium text-gray-900">
                                Products
                            </a>
                            <a href="orders.php" class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                Orders
                            </a>
                        </nav>
                    </div>
                    <div class="flex items-center">
                        <div class="ml-3 relative">
                            <div>
                                <button type="button" class="flex items-center max-w-xs rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary" id="user-menu-button">
                                    <span class="sr-only">Open user menu</span>
                                    <span class="inline-block h-8 w-8 rounded-full overflow-hidden bg-gray-100">
                                        <i class="ri-user-line text-gray-400 text-lg flex items-center justify-center h-full"></i>
                                    </span>
                                    <span class="ml-2 text-gray-700"><?php echo htmlspecialchars($user_name); ?></span>
                                    <i class="ri-arrow-down-s-line ml-1 text-gray-400"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-grow">
            <div class="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
                <div class="px-4 py-6 sm:px-0">
                    <!-- Breadcrumb -->
                    <nav class="flex mb-6" aria-label="Breadcrumb">
                        <ol class="inline-flex items-center space-x-1 md:space-x-3">
                            <li class="inline-flex items-center">
                                <a href="products.php" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-primary">
                                    <i class="ri-product-hunt-line mr-2"></i>
                                    Products
                                </a>
                            </li>
                            <li>
                                <div class="flex items-center">
                                    <i class="ri-arrow-right-s-line text-gray-400"></i>
                                    <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Edit Product</span>
                                </div>
                            </li>
                        </ol>
                    </nav>

                    <!-- Error Messages -->
                    <?php if (!empty($errors)): ?>
                        <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                            <strong class="font-bold">Please fix the following errors:</strong>
                            <ul class="mt-2 list-disc list-inside">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($db_connection_error)): ?>
                        <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline">Database connection error. Please check your configuration.</span>
                        </div>
                    <?php endif; ?>

                    <!-- Edit Product Form -->
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h1 class="text-2xl font-semibold text-gray-900">Edit Product</h1>
                            <p class="mt-1 text-sm text-gray-600">Update the product details below.</p>
                        </div>

                        <form method="POST" enctype="multipart/form-data" class="px-6 py-6 space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Product Name -->
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700">Product Name *</label>
                                    <input type="text" name="name" id="name" required
                                           value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm"
                                           placeholder="Enter product name">
                                </div>

                                <!-- SKU -->
                                <div>
                                    <label for="sku" class="block text-sm font-medium text-gray-700">SKU (Stock Keeping Unit) *</label>
                                    <input type="text" name="sku" id="sku" required
                                           value="<?php echo htmlspecialchars($_POST['sku'] ?? ''); ?>"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm"
                                           placeholder="e.g., PF-001">
                                </div>
                            </div>

                            <!-- Description -->
                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                                <textarea name="description" id="description" rows="3"
                                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm"
                                          placeholder="Enter product description"><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                            </div>

                            <!-- Product Image -->
                            <div>
                                <label for="product_image" class="block text-sm font-medium text-gray-700">Product Image</label>

                                <?php if (!empty($product['image_url'])): ?>
                                    <div class="mt-2 mb-4">
                                        <p class="text-sm text-gray-600 mb-2">Current Image:</p>
                                        <img src="<?php echo htmlspecialchars($imageUpload->getImageUrl($product['image_url'], 'thumbnail')); ?>"
                                             alt="Current product image"
                                             class="h-32 w-32 object-cover rounded-lg border border-gray-300">
                                    </div>
                                <?php endif; ?>

                                <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-primary transition-colors">
                                    <div class="space-y-1 text-center">
                                        <div id="image-preview" class="hidden mb-4">
                                            <img id="preview-img" src="" alt="Preview" class="mx-auto h-32 w-32 object-cover rounded-lg">
                                        </div>
                                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                        <div class="flex text-sm text-gray-600">
                                            <label for="product_image" class="relative cursor-pointer bg-white rounded-md font-medium text-primary hover:text-primary/80 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary">
                                                <span><?php echo !empty($product['image_url']) ? 'Replace image' : 'Upload a file'; ?></span>
                                                <input id="product_image" name="product_image" type="file" class="sr-only" accept="image/*">
                                            </label>
                                            <p class="pl-1">or drag and drop</p>
                                        </div>
                                        <p class="text-xs text-gray-500">PNG, JPG, GIF, WebP up to 5MB</p>
                                    </div>
                                </div>
                                <?php if (isset($errors['image'])): ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo htmlspecialchars($errors['image']); ?></p>
                                <?php endif; ?>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <!-- Category -->
                                <div>
                                    <label for="category" class="block text-sm font-medium text-gray-700">Category *</label>
                                    <select name="category" id="category" required
                                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm">
                                        <option value="">Select a category</option>
                                        <option value="feed" <?php echo (($_POST['category'] ?? '') === 'feed') ? 'selected' : ''; ?>>Feed</option>
                                        <option value="equipment" <?php echo (($_POST['category'] ?? '') === 'equipment') ? 'selected' : ''; ?>>Equipment</option>
                                        <option value="medicine" <?php echo (($_POST['category'] ?? '') === 'medicine') ? 'selected' : ''; ?>>Medicine</option>
                                        <option value="supplements" <?php echo (($_POST['category'] ?? '') === 'supplements') ? 'selected' : ''; ?>>Supplements</option>
                                        <option value="other" <?php echo (($_POST['category'] ?? '') === 'other') ? 'selected' : ''; ?>>Other</option>
                                    </select>
                                </div>

                                <!-- Price -->
                                <div>
                                    <label for="price" class="block text-sm font-medium text-gray-700">Price ($) *</label>
                                    <input type="number" name="price" id="price" step="0.01" min="0" required
                                           value="<?php echo htmlspecialchars($_POST['price'] ?? ''); ?>"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm"
                                           placeholder="0.00">
                                </div>

                                <!-- Status -->
                                <div>
                                    <label for="status" class="block text-sm font-medium text-gray-700">Status *</label>
                                    <select name="status" id="status" required
                                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm">
                                        <option value="active" <?php echo (($_POST['status'] ?? '') === 'active') ? 'selected' : ''; ?>>Active</option>
                                        <option value="inactive" <?php echo (($_POST['status'] ?? '') === 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                                        <option value="discontinued" <?php echo (($_POST['status'] ?? '') === 'discontinued') ? 'selected' : ''; ?>>Discontinued</option>
                                    </select>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Stock Quantity -->
                                <div>
                                    <label for="stock_quantity" class="block text-sm font-medium text-gray-700">Current Stock Quantity *</label>
                                    <input type="number" name="stock_quantity" id="stock_quantity" min="0" required
                                           value="<?php echo htmlspecialchars($_POST['stock_quantity'] ?? ''); ?>"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm"
                                           placeholder="0">
                                </div>

                                <!-- Minimum Stock Level -->
                                <div>
                                    <label for="min_stock_level" class="block text-sm font-medium text-gray-700">Minimum Stock Level *</label>
                                    <input type="number" name="min_stock_level" id="min_stock_level" min="0" required
                                           value="<?php echo htmlspecialchars($_POST['min_stock_level'] ?? ''); ?>"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm"
                                           placeholder="10">
                                    <p class="mt-1 text-sm text-gray-500">Alert when stock falls below this level</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Weight -->
                                <div>
                                    <label for="weight" class="block text-sm font-medium text-gray-700">Weight (kg)</label>
                                    <input type="number" name="weight" id="weight" step="0.01" min="0"
                                           value="<?php echo htmlspecialchars($_POST['weight'] ?? ''); ?>"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm"
                                           placeholder="0.00">
                                </div>

                                <!-- Dimensions -->
                                <div>
                                    <label for="dimensions" class="block text-sm font-medium text-gray-700">Dimensions</label>
                                    <input type="text" name="dimensions" id="dimensions"
                                           value="<?php echo htmlspecialchars($_POST['dimensions'] ?? ''); ?>"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm"
                                           placeholder="e.g., 30cm x 20cm x 15cm">
                                </div>
                            </div>

                            <!-- Product Info -->
                            <?php if ($product): ?>
                            <div class="bg-gray-50 p-4 rounded-md">
                                <h3 class="text-sm font-medium text-gray-700 mb-2">Product Information</h3>
                                <div class="grid grid-cols-2 gap-4 text-sm text-gray-600">
                                    <div>Created: <?php echo date('M j, Y g:i A', strtotime($product['created_at'])); ?></div>
                                    <div>Last Updated: <?php echo date('M j, Y g:i A', strtotime($product['updated_at'])); ?></div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Form Actions -->
                            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                                <a href="products.php"
                                   class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-button text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                    <i class="ri-arrow-left-line mr-2"></i>
                                    Cancel
                                </a>
                                <button type="submit"
                                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-button shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                    <i class="ri-save-line mr-2"></i>
                                    Update Product
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-white">
            <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <p class="text-center text-gray-500 text-sm">
                    &copy; 2025 PiGit. All rights reserved.
                </p>
            </div>
        </footer>
    </div>

    <script>
        // Image preview functionality
        document.getElementById('product_image').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const preview = document.getElementById('image-preview');
            const previewImg = document.getElementById('preview-img');

            if (file) {
                // Validate file type
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                if (!allowedTypes.includes(file.type)) {
                    alert('Please select a valid image file (JPG, PNG, GIF, WebP)');
                    e.target.value = '';
                    preview.classList.add('hidden');
                    return;
                }

                // Validate file size (5MB)
                if (file.size > 5 * 1024 * 1024) {
                    alert('File size must be less than 5MB');
                    e.target.value = '';
                    preview.classList.add('hidden');
                    return;
                }

                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.src = e.target.result;
                    preview.classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            } else {
                preview.classList.add('hidden');
            }
        });

        // Drag and drop functionality
        const dropZone = document.querySelector('.border-dashed');

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        function highlight(e) {
            dropZone.classList.add('border-primary', 'bg-primary/5');
        }

        function unhighlight(e) {
            dropZone.classList.remove('border-primary', 'bg-primary/5');
        }

        dropZone.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length > 0) {
                document.getElementById('product_image').files = files;
                document.getElementById('product_image').dispatchEvent(new Event('change'));
            }
        }

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const price = parseFloat(document.getElementById('price').value);
            const stockQuantity = parseInt(document.getElementById('stock_quantity').value);
            const minStockLevel = parseInt(document.getElementById('min_stock_level').value);

            if (price <= 0) {
                alert('Price must be greater than 0');
                e.preventDefault();
                return;
            }

            if (stockQuantity < 0) {
                alert('Stock quantity cannot be negative');
                e.preventDefault();
                return;
            }

            if (minStockLevel < 0) {
                alert('Minimum stock level cannot be negative');
                e.preventDefault();
                return;
            }
        });
    </script>
</body>
</html>
