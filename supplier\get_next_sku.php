<?php
// API endpoint to get the next available SKU for a given prefix
header('Content-Type: application/json');

// Start session and check authentication
session_start();

// Check if user is logged in and has supplier role
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'supplier') {
    http_response_code(403);
    echo json_encode(['success' => false, 'error' => 'Access denied']);
    exit();
}

// Check if prefix is provided
if (!isset($_GET['prefix']) || empty($_GET['prefix'])) {
    echo json_encode(['success' => false, 'error' => 'Prefix is required']);
    exit();
}

$prefix = strtoupper(trim($_GET['prefix']));

// Validate prefix
$validPrefixes = ['FD', 'EQ', 'MD', 'SP', 'OT'];
if (!in_array($prefix, $validPrefixes)) {
    echo json_encode(['success' => false, 'error' => 'Invalid prefix']);
    exit();
}

try {
    require_once '../config/db_connect.php';
    
    if (isset($db_connection_error)) {
        echo json_encode(['success' => false, 'error' => 'Database connection failed']);
        exit();
    }
    
    // Find the highest existing number for this prefix
    $stmt = $conn->prepare("SELECT sku FROM products WHERE sku LIKE ? ORDER BY sku DESC LIMIT 1");
    $stmt->execute([$prefix . '-%']);
    $lastSku = $stmt->fetchColumn();
    
    $nextNumber = 1;
    
    if ($lastSku) {
        // Extract the number part from the SKU
        if (preg_match('/' . preg_quote($prefix) . '-(\d+)/', $lastSku, $matches)) {
            $nextNumber = intval($matches[1]) + 1;
        }
    }
    
    // Generate the next SKU
    $nextSku = $prefix . '-' . str_pad($nextNumber, 3, '0', STR_PAD_LEFT);
    
    // Double-check that this SKU doesn't exist (in case of concurrent requests)
    $attempts = 0;
    while ($attempts < 10) {
        $stmt = $conn->prepare("SELECT COUNT(*) FROM products WHERE sku = ?");
        $stmt->execute([$nextSku]);
        
        if ($stmt->fetchColumn() == 0) {
            // SKU is available
            break;
        }
        
        // SKU exists, try the next number
        $nextNumber++;
        $nextSku = $prefix . '-' . str_pad($nextNumber, 3, '0', STR_PAD_LEFT);
        $attempts++;
    }
    
    if ($attempts >= 10) {
        // Fallback to timestamp-based SKU
        $timestamp = time();
        $nextSku = $prefix . '-' . substr($timestamp, -6);
    }
    
    echo json_encode([
        'success' => true,
        'sku' => $nextSku,
        'prefix' => $prefix,
        'number' => $nextNumber
    ]);
    
} catch (PDOException $e) {
    echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
} catch (Exception $e) {
    echo json_encode(['success' => false, 'error' => 'Error: ' . $e->getMessage()]);
}
?>
