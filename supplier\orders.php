<?php
// Start session
session_start();

// Check if user is logged in and has supplier role
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'supplier') {
    header("Location: ../login.php");
    exit();
}

// Connect to database
require_once '../config/db_connect.php';

$user_name = $_SESSION['fullname'];
$username = $_SESSION['username'];
$company_name = $_SESSION['company_name'] ?? 'Your Company';
$business_type = $_SESSION['business_type'] ?? 'Supplier';

// Handle success/error messages
$success_message = '';
$error_message = '';

if (isset($_SESSION['success'])) {
    $success_message = $_SESSION['success'];
    unset($_SESSION['success']);
}

if (isset($_SESSION['error'])) {
    $error_message = $_SESSION['error'];
    unset($_SESSION['error']);
}

// Handle order status updates
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'update_status' && isset($_POST['order_id']) && isset($_POST['status'])) {
        $order_id = intval($_POST['order_id']);
        $new_status = $_POST['status'];

        $allowed_statuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'];

        if (in_array($new_status, $allowed_statuses)) {
            try {
                // Verify order belongs to this supplier
                $stmt = $conn->prepare("SELECT id FROM orders WHERE id = ? AND supplier_id = ?");
                $stmt->execute([$order_id, $_SESSION['user_id']]);

                if ($stmt->rowCount() > 0) {
                    $stmt = $conn->prepare("UPDATE orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND supplier_id = ?");
                    $stmt->execute([$new_status, $order_id, $_SESSION['user_id']]);
                    $_SESSION['success'] = "Order status updated successfully!";
                } else {
                    $_SESSION['error'] = "Order not found or access denied.";
                }
            } catch (PDOException $e) {
                $_SESSION['error'] = "Error updating order: " . $e->getMessage();
            }
        } else {
            $_SESSION['error'] = "Invalid status selected.";
        }

        header("Location: orders.php");
        exit();
    }
}

// Get filter parameters
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$date_filter = $_GET['date_range'] ?? 'all';

// Build query conditions
$where_conditions = ["o.supplier_id = ?"];
$params = [$_SESSION['user_id']];

if (!empty($search)) {
    $where_conditions[] = "(o.order_number LIKE ? OR u.fullname LIKE ? OR u.company_name LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

if (!empty($status_filter)) {
    $where_conditions[] = "o.status = ?";
    $params[] = $status_filter;
}

// Date filtering
$date_condition = "";
switch ($date_filter) {
    case 'today':
        $date_condition = "AND DATE(o.created_at) = CURDATE()";
        break;
    case 'week':
        $date_condition = "AND o.created_at >= DATE_SUB(NOW(), INTERVAL 1 WEEK)";
        break;
    case 'month':
        $date_condition = "AND o.created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)";
        break;
}

// Fetch orders
$orders = [];
$stats = [
    'total_orders' => 0,
    'pending_orders' => 0,
    'processing_orders' => 0,
    'completed_orders' => 0,
    'total_revenue' => 0
];

try {
    if (!isset($db_connection_error)) {
        // Get orders with customer information
        $sql = "SELECT o.*, u.fullname as customer_name, u.company_name as customer_company, u.email as customer_email,
                       COUNT(oi.id) as item_count
                FROM orders o
                JOIN users u ON o.retailer_id = u.id
                LEFT JOIN order_items oi ON o.id = oi.order_id
                WHERE " . implode(' AND ', $where_conditions) . " $date_condition
                GROUP BY o.id
                ORDER BY o.created_at DESC";

        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        $orders = $stmt->fetchAll();

        // Calculate statistics
        $stats_sql = "SELECT
                        COUNT(*) as total_orders,
                        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_orders,
                        SUM(CASE WHEN status IN ('confirmed', 'processing') THEN 1 ELSE 0 END) as processing_orders,
                        SUM(CASE WHEN status IN ('shipped', 'delivered') THEN 1 ELSE 0 END) as completed_orders,
                        SUM(total_amount) as total_revenue
                      FROM orders WHERE supplier_id = ?";

        $stmt = $conn->prepare($stats_sql);
        $stmt->execute([$_SESSION['user_id']]);
        $stats = $stmt->fetch();
    }
} catch (PDOException $e) {
    $error_message = "Error fetching orders: " . $e->getMessage();
}

// Function to get status badge class
function getStatusBadge($status) {
    $badges = [
        'pending' => ['class' => 'bg-yellow-100 text-yellow-800', 'icon' => 'ri-time-line'],
        'confirmed' => ['class' => 'bg-blue-100 text-blue-800', 'icon' => 'ri-check-line'],
        'processing' => ['class' => 'bg-purple-100 text-purple-800', 'icon' => 'ri-loader-line'],
        'shipped' => ['class' => 'bg-indigo-100 text-indigo-800', 'icon' => 'ri-truck-line'],
        'delivered' => ['class' => 'bg-green-100 text-green-800', 'icon' => 'ri-check-double-line'],
        'cancelled' => ['class' => 'bg-red-100 text-red-800', 'icon' => 'ri-close-line']
    ];

    return $badges[$status] ?? ['class' => 'bg-gray-100 text-gray-800', 'icon' => 'ri-question-line'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PiGit - Supplier Orders</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>tailwind.config={theme:{extend:{colors:{primary:'#f97316',secondary:'#84cc16'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex flex-col">
        <!-- Header/Navigation -->
        <header class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex">
                        <div class="flex-shrink-0 flex items-center">
                            <a href="/" class="font-['Pacifico'] text-2xl text-primary">PiGit</a>
                        </div>
                        <nav class="ml-6 flex space-x-8">
                            <a href="dashboard.php" class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                Dashboard
                            </a>
                            <a href="products.php" class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                Products
                            </a>
                            <a href="orders.php" class="inline-flex items-center px-1 pt-1 border-b-2 border-primary text-sm font-medium text-gray-900">
                                Orders
                            </a>
                        </nav>
                    </div>
                    <div class="flex items-center">
                        <div class="ml-3 relative">
                            <div>
                                <button type="button" class="flex items-center max-w-xs rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary" id="user-menu-button">
                                    <span class="sr-only">Open user menu</span>
                                    <span class="inline-block h-8 w-8 rounded-full overflow-hidden bg-gray-100">
                                        <i class="ri-user-line text-gray-400 text-lg flex items-center justify-center h-full"></i>
                                    </span>
                                    <span class="ml-2 text-gray-700"><?php echo htmlspecialchars($_SESSION['fullname']); ?></span>
                                    <i class="ri-arrow-down-s-line ml-1 text-gray-400"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-grow">
            <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div class="px-4 py-6 sm:px-0">
                    <!-- Success/Error Messages -->
                    <?php if ($success_message): ?>
                        <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline"><?php echo htmlspecialchars($success_message); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if ($error_message): ?>
                        <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline"><?php echo htmlspecialchars($error_message); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($db_connection_error)): ?>
                        <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline">Database connection error. Please check your configuration.</span>
                        </div>
                    <?php endif; ?>

                    <div class="flex justify-between items-center mb-6">
                        <h1 class="text-2xl font-semibold text-gray-900">Manage Orders</h1>
                        <div class="flex space-x-3">
                            <button onclick="exportOrders()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-button shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none">
                                <i class="ri-download-line mr-2"></i> Export
                            </button>
                            <button onclick="toggleFilters()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-button shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none">
                                <i class="ri-filter-3-line mr-2"></i> Filter
                            </button>
                        </div>
                    </div>

                    <!-- Statistics Dashboard -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="ri-shopping-cart-line text-2xl text-blue-600"></i>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Total Orders</dt>
                                            <dd class="text-lg font-medium text-gray-900"><?php echo $stats['total_orders'] ?? 0; ?></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="ri-time-line text-2xl text-yellow-600"></i>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Pending</dt>
                                            <dd class="text-lg font-medium text-gray-900"><?php echo $stats['pending_orders'] ?? 0; ?></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="ri-loader-line text-2xl text-purple-600"></i>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Processing</dt>
                                            <dd class="text-lg font-medium text-gray-900"><?php echo $stats['processing_orders'] ?? 0; ?></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="ri-check-double-line text-2xl text-green-600"></i>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Completed</dt>
                                            <dd class="text-lg font-medium text-gray-900"><?php echo $stats['completed_orders'] ?? 0; ?></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="ri-money-dollar-circle-line text-2xl text-primary"></i>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                                            <dd class="text-lg font-medium text-gray-900">$<?php echo number_format($stats['total_revenue'] ?? 0, 2); ?></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Filters -->
                    <div id="filters-panel" class="bg-white p-4 rounded-lg shadow mb-6">
                        <form method="GET" action="orders.php" class="flex flex-wrap gap-4 items-end">
                            <div class="flex-1 min-w-[200px]">
                                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search Orders</label>
                                <div class="relative rounded-md shadow-sm">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="ri-search-line text-gray-400"></i>
                                    </div>
                                    <input type="text" name="search" id="search"
                                           value="<?php echo htmlspecialchars($search); ?>"
                                           class="focus:ring-primary focus:border-primary block w-full pl-10 sm:text-sm border-gray-300 rounded-md"
                                           placeholder="Search by order ID or customer">
                                </div>
                            </div>
                            <div class="w-full md:w-auto">
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                                <select id="status" name="status" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                    <option value="">All Status</option>
                                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="confirmed" <?php echo $status_filter === 'confirmed' ? 'selected' : ''; ?>>Confirmed</option>
                                    <option value="processing" <?php echo $status_filter === 'processing' ? 'selected' : ''; ?>>Processing</option>
                                    <option value="shipped" <?php echo $status_filter === 'shipped' ? 'selected' : ''; ?>>Shipped</option>
                                    <option value="delivered" <?php echo $status_filter === 'delivered' ? 'selected' : ''; ?>>Delivered</option>
                                    <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                </select>
                            </div>
                            <div class="w-full md:w-auto">
                                <label for="date_range" class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                                <select id="date_range" name="date_range" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                    <option value="all" <?php echo $date_filter === 'all' ? 'selected' : ''; ?>>All Time</option>
                                    <option value="today" <?php echo $date_filter === 'today' ? 'selected' : ''; ?>>Today</option>
                                    <option value="week" <?php echo $date_filter === 'week' ? 'selected' : ''; ?>>This Week</option>
                                    <option value="month" <?php echo $date_filter === 'month' ? 'selected' : ''; ?>>This Month</option>
                                </select>
                            </div>
                            <div class="flex gap-2">
                                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-button shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none">
                                    <i class="ri-search-line mr-2"></i> Search
                                </button>
                                <a href="orders.php" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-button shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none">
                                    <i class="ri-refresh-line mr-2"></i> Clear
                                </a>
                            </div>
                        </form>
                    </div>

                    <!-- Orders Table -->
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Items</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (empty($orders)): ?>
                                    <tr>
                                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                            <div class="flex flex-col items-center justify-center py-8">
                                                <i class="ri-shopping-cart-line text-4xl text-gray-300 mb-2"></i>
                                                <p class="text-lg font-medium">No orders found</p>
                                                <p class="text-sm">Orders from customers will appear here.</p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($orders as $order): ?>
                                        <?php $statusBadge = getStatusBadge($order['status']); ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">#<?php echo htmlspecialchars($order['order_number']); ?></div>
                                                <div class="text-sm text-gray-500">ID: <?php echo $order['id']; ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div class="h-8 w-8 flex-shrink-0 bg-gray-100 rounded-full flex items-center justify-center">
                                                        <i class="ri-user-line text-gray-500"></i>
                                                    </div>
                                                    <div class="ml-3">
                                                        <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($order['customer_name']); ?></div>
                                                        <div class="text-sm text-gray-500"><?php echo htmlspecialchars($order['customer_company'] ?? $order['customer_email']); ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900"><?php echo date('M j, Y', strtotime($order['created_at'])); ?></div>
                                                <div class="text-sm text-gray-500"><?php echo date('g:i A', strtotime($order['created_at'])); ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900"><?php echo $order['item_count']; ?> items</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">$<?php echo number_format($order['total_amount'], 2); ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $statusBadge['class']; ?>">
                                                    <i class="<?php echo $statusBadge['icon']; ?> mr-1"></i>
                                                    <?php echo ucfirst($order['status']); ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <div class="flex items-center justify-end space-x-2">
                                                    <button onclick="viewOrder(<?php echo $order['id']; ?>)" class="text-primary hover:text-primary/80">
                                                        <i class="ri-eye-line"></i>
                                                    </button>
                                                    <div class="relative inline-block text-left">
                                                        <button onclick="toggleStatusDropdown(<?php echo $order['id']; ?>)" class="text-gray-400 hover:text-gray-600">
                                                            <i class="ri-more-2-line"></i>
                                                        </button>
                                                        <div id="status-dropdown-<?php echo $order['id']; ?>" class="hidden origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                                                            <div class="py-1">
                                                                <div class="px-1 py-2 text-xs text-gray-500 border-b">Update Status</div>
                                                                <?php foreach (['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'] as $status): ?>
                                                                    <?php if ($status !== $order['status']): ?>
                                                                        <button onclick="updateOrderStatus(<?php echo $order['id']; ?>, '<?php echo $status; ?>')"
                                                                                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                                            <i class="<?php echo getStatusBadge($status)['icon']; ?> mr-2"></i>
                                                                            <?php echo ucfirst($status); ?>
                                                                        </button>
                                                                    <?php endif; ?>
                                                                <?php endforeach; ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-white">
            <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <p class="text-center text-gray-500 text-sm">
                    &copy; 2025 PiGit. All rights reserved.
                </p>
            </div>
        </footer>
    </div>

    <!-- Order Details Modal -->
    <div id="orderModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900" id="modalTitle">Order Details</h3>
                    <button onclick="closeOrderModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="ri-close-line text-xl"></i>
                    </button>
                </div>
                <div id="modalContent" class="mt-2">
                    <!-- Order details will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Toggle filters panel
        function toggleFilters() {
            const panel = document.getElementById('filters-panel');
            panel.classList.toggle('hidden');
        }

        // Toggle status dropdown
        function toggleStatusDropdown(orderId) {
            // Close all other dropdowns
            document.querySelectorAll('[id^="status-dropdown-"]').forEach(dropdown => {
                if (dropdown.id !== `status-dropdown-${orderId}`) {
                    dropdown.classList.add('hidden');
                }
            });

            // Toggle current dropdown
            const dropdown = document.getElementById(`status-dropdown-${orderId}`);
            dropdown.classList.toggle('hidden');
        }

        // Update order status
        function updateOrderStatus(orderId, newStatus) {
            if (confirm(`Are you sure you want to update this order status to "${newStatus}"?`)) {
                // Create form and submit
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'orders.php';

                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'update_status';

                const orderIdInput = document.createElement('input');
                orderIdInput.type = 'hidden';
                orderIdInput.name = 'order_id';
                orderIdInput.value = orderId;

                const statusInput = document.createElement('input');
                statusInput.type = 'hidden';
                statusInput.name = 'status';
                statusInput.value = newStatus;

                form.appendChild(actionInput);
                form.appendChild(orderIdInput);
                form.appendChild(statusInput);

                document.body.appendChild(form);
                form.submit();
            }

            // Hide dropdown
            document.getElementById(`status-dropdown-${orderId}`).classList.add('hidden');
        }

        // View order details
        function viewOrder(orderId) {
            // Show loading state
            document.getElementById('modalTitle').textContent = 'Loading Order Details...';
            document.getElementById('modalContent').innerHTML = '<div class="text-center py-4"><i class="ri-loader-4-line animate-spin text-2xl text-primary"></i></div>';
            document.getElementById('orderModal').classList.remove('hidden');

            // Fetch order details via AJAX
            fetch(`view_order.php?id=${orderId}`)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('modalTitle').textContent = `Order #${orderId}`;
                    document.getElementById('modalContent').innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('modalContent').innerHTML = '<div class="text-red-600">Error loading order details. Please try again.</div>';
                });
        }

        // Close order modal
        function closeOrderModal() {
            document.getElementById('orderModal').classList.add('hidden');
        }

        // Export orders
        function exportOrders() {
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('export', 'csv');
            window.location.href = currentUrl.toString();
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.relative')) {
                document.querySelectorAll('[id^="status-dropdown-"]').forEach(dropdown => {
                    dropdown.classList.add('hidden');
                });
            }
        });

        // Close modal when clicking outside
        document.getElementById('orderModal').addEventListener('click', function(event) {
            if (event.target === this) {
                closeOrderModal();
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            // Escape key closes modal
            if (event.key === 'Escape') {
                closeOrderModal();
            }

            // Ctrl/Cmd + F focuses search
            if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
                event.preventDefault();
                document.getElementById('search').focus();
            }
        });

        // Auto-refresh orders every 30 seconds
        let autoRefreshInterval;

        function startAutoRefresh() {
            autoRefreshInterval = setInterval(() => {
                // Only refresh if no modals are open
                if (document.getElementById('orderModal').classList.contains('hidden')) {
                    window.location.reload();
                }
            }, 30000); // 30 seconds
        }

        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        }

        // Start auto-refresh on page load
        document.addEventListener('DOMContentLoaded', function() {
            startAutoRefresh();
        });

        // Stop auto-refresh when page is hidden
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                stopAutoRefresh();
            } else {
                startAutoRefresh();
            }
        });

        // Real-time search
        let searchTimeout;
        document.getElementById('search').addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.form.submit();
            }, 500); // Wait 500ms after user stops typing
        });

        // Status filter change
        document.getElementById('status').addEventListener('change', function() {
            this.form.submit();
        });

        // Date range filter change
        document.getElementById('date_range').addEventListener('change', function() {
            this.form.submit();
        });
    </script>
</body>
</html>