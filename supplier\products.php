<?php
// Start session
session_start();

// Check if user is logged in and has supplier role
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'supplier') {
    header("Location: ../login.php");
    exit();
}

$user_name = $_SESSION['fullname'];
$username = $_SESSION['username'];
$company_name = $_SESSION['company_name'] ?? 'Your Company';
$business_type = $_SESSION['business_type'] ?? 'Supplier';

// Connect to database
require_once '../config/db_connect.php';
require_once '../includes/smart_image_upload.php';

// Handle success/error messages
$success_message = '';
$error_message = '';
$imageUpload = new SmartImageUpload();

if (isset($_SESSION['success'])) {
    $success_message = $_SESSION['success'];
    unset($_SESSION['success']);
}

if (isset($_SESSION['error'])) {
    $error_message = $_SESSION['error'];
    unset($_SESSION['error']);
}

// Fetch products for this supplier
$products = [];
$stats = [
    'total_products' => 0,
    'in_stock' => 0,
    'low_stock' => 0,
    'out_of_stock' => 0,
    'total_value' => 0
];

try {
    if (!isset($db_connection_error)) {
        $stmt = $conn->prepare("SELECT * FROM products WHERE supplier_id = ? ORDER BY created_at DESC");
        $stmt->execute([$_SESSION['user_id']]);
        $products = $stmt->fetchAll();

        // Calculate statistics
        $stats['total_products'] = count($products);
        foreach ($products as $product) {
            $stats['total_value'] += $product['price'] * $product['stock_quantity'];

            if ($product['stock_quantity'] <= 0) {
                $stats['out_of_stock']++;
            } elseif ($product['stock_quantity'] <= $product['min_stock_level']) {
                $stats['low_stock']++;
            } else {
                $stats['in_stock']++;
            }
        }
    }
} catch (PDOException $e) {
    $error_message = "Error fetching products: " . $e->getMessage();
}

// Function to get stock status
function getStockStatus($stock, $minStock) {
    if ($stock <= 0) {
        return ['status' => 'Out of Stock', 'class' => 'bg-red-100 text-red-800'];
    } elseif ($stock <= $minStock) {
        return ['status' => 'Low Stock', 'class' => 'bg-yellow-100 text-yellow-800'];
    } else {
        return ['status' => 'In Stock', 'class' => 'bg-green-100 text-green-800'];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PiGit - Supplier Products</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>tailwind.config={theme:{extend:{colors:{primary:'#f97316',secondary:'#84cc16'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex flex-col">
        <!-- Header/Navigation -->
        <header class="bg-white shadow">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex">
                        <div class="flex-shrink-0 flex items-center">
                            <a href="/" class="font-['Pacifico'] text-2xl text-primary">PiGit</a>
                        </div>
                        <nav class="ml-6 flex space-x-8">
                            <a href="dashboard.php" class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                Dashboard
                            </a>
                            <a href="products.php" class="inline-flex items-center px-1 pt-1 border-b-2 border-primary text-sm font-medium text-gray-900">
                                Products
                            </a>
                            <a href="orders.php" class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                                Orders
                            </a>
                        </nav>
                    </div>
                    <div class="flex items-center">
                        <div class="ml-3 relative">
                            <div>
                                <button type="button" class="flex items-center max-w-xs rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary" id="user-menu-button">
                                    <span class="sr-only">Open user menu</span>
                                    <span class="inline-block h-8 w-8 rounded-full overflow-hidden bg-gray-100">
                                        <i class="ri-user-line text-gray-400 text-lg flex items-center justify-center h-full"></i>
                                    </span>
                                    <span class="ml-2 text-gray-700"><?php echo htmlspecialchars($_SESSION['fullname']); ?></span>
                                    <i class="ri-arrow-down-s-line ml-1 text-gray-400"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-grow">
            <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div class="px-4 py-6 sm:px-0">
                    <!-- Success/Error Messages -->
                    <?php if ($success_message): ?>
                        <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline"><?php echo htmlspecialchars($success_message); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if ($error_message): ?>
                        <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline"><?php echo htmlspecialchars($error_message); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($db_connection_error)): ?>
                        <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline">Database connection error. Please check your configuration.</span>
                        </div>
                    <?php endif; ?>

                    <div class="flex justify-between items-center mb-6">
                        <h1 class="text-2xl font-semibold text-gray-900">Manage Products</h1>
                        <a href="add_product.php" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-button shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none">
                            <i class="ri-add-line mr-2"></i> Add New Product
                        </a>
                    </div>

                    <!-- Statistics Dashboard -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="ri-product-hunt-line text-2xl text-blue-600"></i>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Total Products</dt>
                                            <dd class="text-lg font-medium text-gray-900"><?php echo $stats['total_products']; ?></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="ri-checkbox-circle-line text-2xl text-green-600"></i>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">In Stock</dt>
                                            <dd class="text-lg font-medium text-gray-900"><?php echo $stats['in_stock']; ?></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="ri-error-warning-line text-2xl text-yellow-600"></i>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Low Stock</dt>
                                            <dd class="text-lg font-medium text-gray-900"><?php echo $stats['low_stock']; ?></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white overflow-hidden shadow rounded-lg">
                            <div class="p-5">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <i class="ri-money-dollar-circle-line text-2xl text-primary"></i>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">Inventory Value</dt>
                                            <dd class="text-lg font-medium text-gray-900">$<?php echo number_format($stats['total_value'], 2); ?></dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Product Filters -->
                    <div class="bg-white p-4 rounded-lg shadow mb-6">
                        <div class="flex flex-wrap gap-4 items-center">
                            <div class="flex-1 min-w-[200px]">
                                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search Products</label>
                                <div class="relative rounded-md shadow-sm">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="ri-search-line text-gray-400"></i>
                                    </div>
                                    <input type="text" name="search" id="search" class="focus:ring-primary focus:border-primary block w-full pl-10 sm:text-sm border-gray-300 rounded-md" placeholder="Search by name or SKU">
                                </div>
                            </div>
                            <div class="w-full md:w-auto">
                                <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                                <select id="category" name="category" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                    <option value="">All Categories</option>
                                    <option value="feed">Feed</option>
                                    <option value="equipment">Equipment</option>
                                    <option value="medicine">Medicine</option>
                                    <option value="supplements">Supplements</option>
                                </select>
                            </div>
                            <div class="w-full md:w-auto">
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                                <select id="status" name="status" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                    <option value="">All Status</option>
                                    <option value="in_stock">In Stock</option>
                                    <option value="low_stock">Low Stock</option>
                                    <option value="out_of_stock">Out of Stock</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Products Table -->
                    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (empty($products)): ?>
                                    <tr>
                                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                            <div class="flex flex-col items-center justify-center py-8">
                                                <i class="ri-inbox-line text-4xl text-gray-300 mb-2"></i>
                                                <p class="text-lg font-medium">No products found</p>
                                                <p class="text-sm">Start by adding your first product to get started.</p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($products as $product): ?>
                                        <?php $stockStatus = getStockStatus($product['stock_quantity'], $product['min_stock_level']); ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div class="h-10 w-10 flex-shrink-0 bg-gray-100 rounded overflow-hidden">
                                                        <?php if (!empty($product['image_url'])): ?>
                                                            <img src="<?php echo htmlspecialchars($imageUpload->getImageUrl($product['image_url'], 'thumbnail')); ?>"
                                                                 alt="<?php echo htmlspecialchars($product['name']); ?>"
                                                                 class="h-10 w-10 object-cover">
                                                        <?php else: ?>
                                                            <i class="ri-product-hunt-line text-gray-500 flex items-center justify-center h-full"></i>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="ml-4">
                                                        <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($product['name']); ?></div>
                                                        <div class="text-sm text-gray-500">SKU: <?php echo htmlspecialchars($product['sku']); ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900"><?php echo ucfirst(htmlspecialchars($product['category'])); ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900">$<?php echo number_format($product['price'], 2); ?></div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900"><?php echo $product['stock_quantity']; ?> units</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $stockStatus['class']; ?>">
                                                    <?php echo $stockStatus['status']; ?>
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <a href="edit_product.php?id=<?php echo $product['id']; ?>" class="text-primary hover:text-primary/80 mr-3">Edit</a>
                                                <a href="delete_product.php?id=<?php echo $product['id']; ?>"
                                                   class="text-red-600 hover:text-red-900">
                                                   Delete
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-700">
                                    <?php if (!empty($products)): ?>
                                        Showing <span class="font-medium">1</span> to <span class="font-medium"><?php echo count($products); ?></span> of <span class="font-medium"><?php echo count($products); ?></span> results
                                    <?php else: ?>
                                        No products to display
                                    <?php endif; ?>
                                </div>
                                <div class="flex-1 flex justify-end">
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">Previous</span>
                                            <i class="ri-arrow-left-s-line"></i>
                                        </a>
                                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">1</a>
                                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">2</a>
                                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">3</a>
                                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">Next</span>
                                            <i class="ri-arrow-right-s-line"></i>
                                        </a>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-white">
            <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <p class="text-center text-gray-500 text-sm">
                    &copy; 2025 PiGit. All rights reserved.
                </p>
            </div>
        </footer>
    </div>

    <script>
        // Enhanced delete confirmation
        function confirmDelete(productName, deleteUrl) {
            if (confirm(`Are you sure you want to delete "${productName}"?\n\nThis action cannot be undone.`)) {
                // Show loading state
                const deleteLink = event.target;
                deleteLink.innerHTML = '<i class="ri-loader-4-line animate-spin"></i> Deleting...';
                deleteLink.style.pointerEvents = 'none';

                // Navigate to delete URL
                window.location.href = deleteUrl;
            }
            return false;
        }

        // Add loading states to all delete links
        document.addEventListener('DOMContentLoaded', function() {
            const deleteLinks = document.querySelectorAll('a[href*="delete_product.php"]');
            deleteLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const productRow = this.closest('tr');
                    const productName = productRow.querySelector('.text-sm.font-medium').textContent;
                    const deleteUrl = this.href;

                    if (confirm(`Are you sure you want to delete "${productName}"?\n\nThis action cannot be undone.`)) {
                        // Show loading state
                        this.innerHTML = '<i class="ri-loader-4-line animate-spin"></i> Deleting...';
                        this.style.pointerEvents = 'none';
                        this.classList.add('opacity-50');

                        // Navigate to delete URL
                        window.location.href = deleteUrl;
                    }
                });
            });
        });
    </script>
</body>
</html>