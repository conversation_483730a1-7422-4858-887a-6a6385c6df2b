<?php
// Start session
session_start();

// Check if user is logged in and has supplier role
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'supplier') {
    http_response_code(403);
    echo '<div class="text-red-600">Access denied</div>';
    exit();
}

// Check if order ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo '<div class="text-red-600">No order ID provided</div>';
    exit();
}

$order_id = intval($_GET['id']);

// Connect to database
require_once '../config/db_connect.php';

try {
    if (isset($db_connection_error)) {
        echo '<div class="text-red-600">Database connection error</div>';
        exit();
    }

    // Get order details
    $sql = "SELECT o.*, u.fullname as customer_name, u.company_name as customer_company, 
                   u.email as customer_email, u.phone as customer_phone
            FROM orders o 
            JOIN users u ON o.retailer_id = u.id 
            WHERE o.id = ? AND o.supplier_id = ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$order_id, $_SESSION['user_id']]);
    $order = $stmt->fetch();
    
    if (!$order) {
        echo '<div class="text-red-600">Order not found or access denied</div>';
        exit();
    }
    
    // Get order items
    $sql = "SELECT oi.*, p.name as product_name, p.sku as product_sku, p.image_url
            FROM order_items oi 
            JOIN products p ON oi.product_id = p.id 
            WHERE oi.order_id = ?
            ORDER BY oi.id";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$order_id]);
    $order_items = $stmt->fetchAll();
    
    // Function to get status badge
    function getStatusBadge($status) {
        $badges = [
            'pending' => ['class' => 'bg-yellow-100 text-yellow-800', 'icon' => 'ri-time-line'],
            'confirmed' => ['class' => 'bg-blue-100 text-blue-800', 'icon' => 'ri-check-line'],
            'processing' => ['class' => 'bg-purple-100 text-purple-800', 'icon' => 'ri-loader-line'],
            'shipped' => ['class' => 'bg-indigo-100 text-indigo-800', 'icon' => 'ri-truck-line'],
            'delivered' => ['class' => 'bg-green-100 text-green-800', 'icon' => 'ri-check-double-line'],
            'cancelled' => ['class' => 'bg-red-100 text-red-800', 'icon' => 'ri-close-line']
        ];
        
        return $badges[$status] ?? ['class' => 'bg-gray-100 text-gray-800', 'icon' => 'ri-question-line'];
    }
    
    $statusBadge = getStatusBadge($order['status']);
    
} catch (PDOException $e) {
    echo '<div class="text-red-600">Error fetching order details: ' . htmlspecialchars($e->getMessage()) . '</div>';
    exit();
}
?>

<div class="space-y-6">
    <!-- Order Header -->
    <div class="bg-gray-50 p-4 rounded-lg">
        <div class="flex items-center justify-between mb-4">
            <div>
                <h4 class="text-lg font-medium text-gray-900">Order #<?php echo htmlspecialchars($order['order_number']); ?></h4>
                <p class="text-sm text-gray-500">Order ID: <?php echo $order['id']; ?></p>
            </div>
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <?php echo $statusBadge['class']; ?>">
                <i class="<?php echo $statusBadge['icon']; ?> mr-1"></i>
                <?php echo ucfirst($order['status']); ?>
            </span>
        </div>
        
        <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
                <span class="font-medium text-gray-700">Order Date:</span>
                <span class="text-gray-900"><?php echo date('M j, Y g:i A', strtotime($order['created_at'])); ?></span>
            </div>
            <div>
                <span class="font-medium text-gray-700">Last Updated:</span>
                <span class="text-gray-900"><?php echo date('M j, Y g:i A', strtotime($order['updated_at'])); ?></span>
            </div>
        </div>
    </div>

    <!-- Customer Information -->
    <div>
        <h5 class="text-md font-medium text-gray-900 mb-3">Customer Information</h5>
        <div class="bg-white border border-gray-200 rounded-lg p-4">
            <div class="flex items-center mb-3">
                <div class="h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                    <i class="ri-user-line text-gray-500"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($order['customer_name']); ?></p>
                    <?php if ($order['customer_company']): ?>
                        <p class="text-sm text-gray-500"><?php echo htmlspecialchars($order['customer_company']); ?></p>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                    <span class="font-medium text-gray-700">Email:</span>
                    <span class="text-gray-900"><?php echo htmlspecialchars($order['customer_email']); ?></span>
                </div>
                <?php if ($order['customer_phone']): ?>
                <div>
                    <span class="font-medium text-gray-700">Phone:</span>
                    <span class="text-gray-900"><?php echo htmlspecialchars($order['customer_phone']); ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Shipping Address -->
    <?php if ($order['shipping_address']): ?>
    <div>
        <h5 class="text-md font-medium text-gray-900 mb-3">Shipping Address</h5>
        <div class="bg-white border border-gray-200 rounded-lg p-4">
            <p class="text-sm text-gray-900 whitespace-pre-line"><?php echo htmlspecialchars($order['shipping_address']); ?></p>
        </div>
    </div>
    <?php endif; ?>

    <!-- Order Items -->
    <div>
        <h5 class="text-md font-medium text-gray-900 mb-3">Order Items</h5>
        <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
            <?php if (empty($order_items)): ?>
                <div class="p-4 text-center text-gray-500">
                    <p>No items found for this order</p>
                </div>
            <?php else: ?>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Product</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">SKU</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Qty</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Unit Price</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Total</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <?php foreach ($order_items as $item): ?>
                            <tr>
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="h-8 w-8 flex-shrink-0 bg-gray-100 rounded overflow-hidden">
                                            <?php if ($item['image_url']): ?>
                                                <img src="../uploads/products/thumbnails/<?php echo htmlspecialchars($item['image_url']); ?>" 
                                                     alt="<?php echo htmlspecialchars($item['product_name']); ?>" 
                                                     class="h-8 w-8 object-cover">
                                            <?php else: ?>
                                                <i class="ri-product-hunt-line text-gray-500 flex items-center justify-center h-full"></i>
                                            <?php endif; ?>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($item['product_name']); ?></p>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-900"><?php echo htmlspecialchars($item['product_sku']); ?></td>
                                <td class="px-4 py-3 text-sm text-gray-900"><?php echo $item['quantity']; ?></td>
                                <td class="px-4 py-3 text-sm text-gray-900">$<?php echo number_format($item['unit_price'], 2); ?></td>
                                <td class="px-4 py-3 text-sm font-medium text-gray-900">$<?php echo number_format($item['total_price'], 2); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot class="bg-gray-50">
                        <tr>
                            <td colspan="4" class="px-4 py-3 text-sm font-medium text-gray-900 text-right">Total Amount:</td>
                            <td class="px-4 py-3 text-sm font-bold text-gray-900">$<?php echo number_format($order['total_amount'], 2); ?></td>
                        </tr>
                    </tfoot>
                </table>
            <?php endif; ?>
        </div>
    </div>

    <!-- Order Notes -->
    <?php if ($order['notes']): ?>
    <div>
        <h5 class="text-md font-medium text-gray-900 mb-3">Order Notes</h5>
        <div class="bg-white border border-gray-200 rounded-lg p-4">
            <p class="text-sm text-gray-900 whitespace-pre-line"><?php echo htmlspecialchars($order['notes']); ?></p>
        </div>
    </div>
    <?php endif; ?>

    <!-- Action Buttons -->
    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button onclick="printOrder(<?php echo $order['id']; ?>)" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            <i class="ri-printer-line mr-2"></i>
            Print Order
        </button>
        
        <?php if ($order['status'] !== 'delivered' && $order['status'] !== 'cancelled'): ?>
            <div class="relative inline-block text-left">
                <button onclick="toggleModalStatusDropdown()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90">
                    <i class="ri-edit-line mr-2"></i>
                    Update Status
                </button>
                <div id="modal-status-dropdown" class="hidden origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-20">
                    <div class="py-1">
                        <?php foreach (['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'] as $status): ?>
                            <?php if ($status !== $order['status']): ?>
                                <button onclick="updateOrderStatusFromModal(<?php echo $order['id']; ?>, '<?php echo $status; ?>')" 
                                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="<?php echo getStatusBadge($status)['icon']; ?> mr-2"></i>
                                    <?php echo ucfirst($status); ?>
                                </button>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function toggleModalStatusDropdown() {
    document.getElementById('modal-status-dropdown').classList.toggle('hidden');
}

function updateOrderStatusFromModal(orderId, newStatus) {
    if (confirm(`Are you sure you want to update this order status to "${newStatus}"?`)) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'orders.php';
        
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'update_status';
        
        const orderIdInput = document.createElement('input');
        orderIdInput.type = 'hidden';
        orderIdInput.name = 'order_id';
        orderIdInput.value = orderId;
        
        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status';
        statusInput.value = newStatus;
        
        form.appendChild(actionInput);
        form.appendChild(orderIdInput);
        form.appendChild(statusInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}

function printOrder(orderId) {
    window.open(`print_order.php?id=${orderId}`, '_blank', 'width=800,height=600');
}
</script>
