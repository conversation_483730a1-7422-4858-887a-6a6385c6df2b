<?php
// Complete system test - creates users, logs in, and tests product addition
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html><head><title>Complete System Test</title>";
echo "<style>body{font-family:Arial;max-width:1000px;margin:20px auto;padding:20px;} .success{background:#e6ffe6;color:green;padding:10px;border-radius:4px;margin:10px 0;} .error{background:#ffe6e6;color:red;padding:10px;border-radius:4px;margin:10px 0;} .info{background:#e6f3ff;color:blue;padding:10px;border-radius:4px;margin:10px 0;} .warning{background:#fff3cd;color:#856404;padding:10px;border-radius:4px;margin:10px 0;} table{width:100%;border-collapse:collapse;margin:20px 0;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background:#f5f5f5;} .btn{display:inline-block;padding:8px 16px;margin:4px;text-decoration:none;border-radius:4px;color:white;} .btn-primary{background:#007bff;} .btn-success{background:#28a745;} .btn-danger{background:#dc3545;}</style>";
echo "</head><body>";

echo "<h1>🧪 Complete System Test</h1>";

try {
    require_once 'config/db_connect.php';
    
    if (isset($db_connection_error)) {
        echo "<div class='error'>❌ Database connection failed!</div>";
        exit;
    }
    
    echo "<div class='success'>✅ Database connected successfully!</div>";
    
    // Step 1: Ensure test users exist
    echo "<h2>👥 Step 1: Ensure Test Users Exist</h2>";
    
    // Check if test supplier exists
    $stmt = $conn->prepare("SELECT id, username, role FROM users WHERE username = 'testsupplier'");
    $stmt->execute();
    $testSupplier = $stmt->fetch();
    
    if (!$testSupplier) {
        echo "<div class='warning'>⚠️ Test supplier doesn't exist. Creating...</div>";
        
        // Create test supplier
        $hashedPassword = password_hash('password123', PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (fullname, username, email, password, role, company_name, business_type) 
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            'Test Supplier',
            'testsupplier',
            '<EMAIL>',
            $hashedPassword,
            'supplier',
            'Test Supply Company',
            'Feed Supplier'
        ]);
        
        $testSupplierId = $conn->lastInsertId();
        echo "<div class='success'>✅ Created test supplier with ID: $testSupplierId</div>";
    } else {
        echo "<div class='success'>✅ Test supplier exists with ID: {$testSupplier['id']}</div>";
        $testSupplierId = $testSupplier['id'];
    }
    
    // Check if test retailer exists
    $stmt = $conn->prepare("SELECT id, username, role FROM users WHERE username = 'testretailer'");
    $stmt->execute();
    $testRetailer = $stmt->fetch();
    
    if (!$testRetailer) {
        echo "<div class='warning'>⚠️ Test retailer doesn't exist. Creating...</div>";
        
        // Create test retailer
        $hashedPassword = password_hash('password123', PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (fullname, username, email, password, role, company_name, business_type) 
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            'Test Retailer',
            'testretailer',
            '<EMAIL>',
            $hashedPassword,
            'retailer',
            'Test Retail Store',
            'Farm Products Retailer'
        ]);
        
        $testRetailerId = $conn->lastInsertId();
        echo "<div class='success'>✅ Created test retailer with ID: $testRetailerId</div>";
    } else {
        echo "<div class='success'>✅ Test retailer exists with ID: {$testRetailer['id']}</div>";
        $testRetailerId = $testRetailer['id'];
    }
    
    // Step 2: Test login simulation
    echo "<h2>🔐 Step 2: Test Login Simulation</h2>";
    
    session_start();
    
    // Simulate supplier login
    $_SESSION['user_id'] = $testSupplierId;
    $_SESSION['username'] = 'testsupplier';
    $_SESSION['fullname'] = 'Test Supplier';
    $_SESSION['email'] = '<EMAIL>';
    $_SESSION['role'] = 'supplier';
    $_SESSION['company_name'] = 'Test Supply Company';
    $_SESSION['business_type'] = 'Feed Supplier';
    $_SESSION['login_time'] = time();
    
    echo "<div class='success'>✅ Simulated supplier login - Session user_id: {$_SESSION['user_id']}</div>";
    
    // Verify session user exists in database
    $stmt = $conn->prepare("SELECT id, username, role FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $sessionUser = $stmt->fetch();
    
    if ($sessionUser) {
        echo "<div class='success'>✅ Session user verified in database</div>";
    } else {
        echo "<div class='error'>❌ Session user not found in database!</div>";
    }
    
    // Step 3: Test product addition
    echo "<h2>📦 Step 3: Test Product Addition</h2>";
    
    // Simulate adding a product
    $productData = [
        'name' => 'Test Product ' . date('Y-m-d H:i:s'),
        'sku' => 'TEST-' . time(),
        'description' => 'This is a test product created by the system test',
        'category' => 'feed',
        'price' => 29.99,
        'stock_quantity' => 100,
        'min_stock_level' => 10,
        'weight' => 5.5,
        'dimensions' => '30cm x 20cm x 15cm'
    ];
    
    try {
        // First verify supplier exists
        $stmt = $conn->prepare("SELECT id, role FROM users WHERE id = ? AND role = 'supplier'");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch();
        
        if (!$user) {
            echo "<div class='error'>❌ Supplier verification failed</div>";
        } else {
            echo "<div class='success'>✅ Supplier verification passed</div>";
            
            // Insert test product
            $sql = "INSERT INTO products (supplier_id, name, sku, description, category, price, stock_quantity, min_stock_level, weight, dimensions) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->execute([
                $_SESSION['user_id'],
                $productData['name'],
                $productData['sku'],
                $productData['description'],
                $productData['category'],
                $productData['price'],
                $productData['stock_quantity'],
                $productData['min_stock_level'],
                $productData['weight'],
                $productData['dimensions']
            ]);
            
            $productId = $conn->lastInsertId();
            echo "<div class='success'>✅ Test product created successfully with ID: $productId</div>";
            echo "<div class='info'>📋 Product: {$productData['name']} (SKU: {$productData['sku']})</div>";
        }
        
    } catch (PDOException $e) {
        echo "<div class='error'>❌ Product creation failed: " . $e->getMessage() . "</div>";
        
        // Detailed error analysis
        if (strpos($e->getMessage(), 'foreign key constraint') !== false) {
            echo "<div class='error'>🔍 Foreign key constraint violation detected</div>";
            echo "<div class='info'>This means the supplier_id ({$_SESSION['user_id']}) doesn't exist in the users table</div>";
        }
    }
    
    // Step 4: Verify products
    echo "<h2>📊 Step 4: Verify Products</h2>";
    
    try {
        $stmt = $conn->prepare("SELECT p.*, u.fullname as supplier_name FROM products p JOIN users u ON p.supplier_id = u.id WHERE p.supplier_id = ?");
        $stmt->execute([$testSupplierId]);
        $products = $stmt->fetchAll();
        
        if (empty($products)) {
            echo "<div class='warning'>⚠️ No products found for test supplier</div>";
        } else {
            echo "<div class='success'>✅ Found " . count($products) . " products for test supplier</div>";
            
            echo "<table>";
            echo "<tr><th>ID</th><th>Name</th><th>SKU</th><th>Category</th><th>Price</th><th>Stock</th><th>Created</th></tr>";
            foreach ($products as $product) {
                echo "<tr>";
                echo "<td>{$product['id']}</td>";
                echo "<td>{$product['name']}</td>";
                echo "<td>{$product['sku']}</td>";
                echo "<td>{$product['category']}</td>";
                echo "<td>\${$product['price']}</td>";
                echo "<td>{$product['stock_quantity']}</td>";
                echo "<td>" . date('M j, Y H:i', strtotime($product['created_at'])) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error fetching products: " . $e->getMessage() . "</div>";
    }
    
    // Step 5: Test orders
    echo "<h2>🛒 Step 5: Test Sample Orders</h2>";
    
    if (!empty($products)) {
        try {
            // Create a sample order
            $orderNumber = 'TEST-' . date('YmdHis');
            $totalAmount = 59.98; // 2 items at $29.99 each
            
            $sql = "INSERT INTO orders (retailer_id, supplier_id, order_number, status, total_amount, shipping_address, notes) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->execute([
                $testRetailerId,
                $testSupplierId,
                $orderNumber,
                'pending',
                $totalAmount,
                "123 Test Street\nTest City, TS 12345\nUnited States",
                'Test order created by system test'
            ]);
            
            $orderId = $conn->lastInsertId();
            
            // Add order items
            $product = $products[0]; // Use first product
            $sql = "INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price) 
                    VALUES (?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->execute([
                $orderId,
                $product['id'],
                2,
                $product['price'],
                2 * $product['price']
            ]);
            
            echo "<div class='success'>✅ Created test order: $orderNumber (ID: $orderId)</div>";
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ Error creating test order: " . $e->getMessage() . "</div>";
        }
    }
    
    // Step 6: System summary
    echo "<h2>📈 Step 6: System Summary</h2>";
    
    $stats = [];
    
    try {
        $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'supplier'");
        $stats['suppliers'] = $stmt->fetchColumn();
        
        $stmt = $conn->query("SELECT COUNT(*) FROM users WHERE role = 'retailer'");
        $stats['retailers'] = $stmt->fetchColumn();
        
        $stmt = $conn->query("SELECT COUNT(*) FROM products");
        $stats['products'] = $stmt->fetchColumn();
        
        $stmt = $conn->query("SELECT COUNT(*) FROM orders");
        $stats['orders'] = $stmt->fetchColumn();
        
        echo "<div class='info'>";
        echo "<strong>System Statistics:</strong><br>";
        echo "👥 Suppliers: {$stats['suppliers']}<br>";
        echo "🏪 Retailers: {$stats['retailers']}<br>";
        echo "📦 Products: {$stats['products']}<br>";
        echo "🛒 Orders: {$stats['orders']}<br>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error getting statistics: " . $e->getMessage() . "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ System test failed: " . $e->getMessage() . "</div>";
}

echo "<h2>🎯 Test Results & Next Steps</h2>";

echo "<div class='success'>";
echo "<strong>✅ System Test Complete!</strong><br>";
echo "The foreign key issue should now be resolved. You can now:";
echo "</div>";

echo "<div style='margin:20px 0;'>";
echo "<a href='logout.php' class='btn btn-danger'>Logout Current Session</a> ";
echo "<a href='login.php' class='btn btn-primary'>Login as Supplier</a> ";
echo "<a href='supplier/add_product.php' class='btn btn-success'>Add Product</a> ";
echo "<a href='supplier/products.php' class='btn btn-primary'>View Products</a> ";
echo "<a href='supplier/orders.php' class='btn btn-primary'>View Orders</a>";
echo "</div>";

echo "<div class='info'>";
echo "<strong>Test Credentials:</strong><br>";
echo "• <strong>Supplier:</strong> Username: <code>testsupplier</code>, Password: <code>password123</code><br>";
echo "• <strong>Retailer:</strong> Username: <code>testretailer</code>, Password: <code>password123</code>";
echo "</div>";

echo "</body></html>";
?>
