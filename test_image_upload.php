<?php
// Test script for image upload functionality
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Image Upload System</title>";
echo "<style>body{font-family:Arial;max-width:800px;margin:20px auto;padding:20px;} .success{background:#e6ffe6;color:green;padding:10px;border-radius:4px;margin:10px 0;} .error{background:#ffe6e6;color:red;padding:10px;border-radius:4px;margin:10px 0;} .info{background:#e6f3ff;color:blue;padding:10px;border-radius:4px;margin:10px 0;} .warning{background:#fff3cd;color:#856404;padding:10px;border-radius:4px;margin:10px 0;}</style>";
echo "</head><body>";

echo "<h1>🖼️ Image Upload System Test</h1>";

// Test 1: Check if image upload class exists
echo "<h2>📁 File System Check</h2>";

$requiredFiles = [
    'includes/image_upload.php' => 'Image Upload Class',
    'supplier/add_product.php' => 'Add Product Form',
    'supplier/edit_product.php' => 'Edit Product Form'
];

foreach ($requiredFiles as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ $description: $file</div>";
    } else {
        echo "<div class='error'>❌ Missing $description: $file</div>";
    }
}

// Test 2: Check directory structure
echo "<h2>📂 Directory Structure Check</h2>";

$requiredDirs = [
    'uploads/' => 'Main uploads directory',
    'uploads/products/' => 'Products directory',
    'uploads/products/thumbnails/' => 'Thumbnails directory',
    'uploads/products/original/' => 'Original images directory'
];

foreach ($requiredDirs as $dir => $description) {
    if (is_dir($dir)) {
        $writable = is_writable($dir) ? '✅ Writable' : '❌ Not writable';
        echo "<div class='success'>✅ $description: $dir ($writable)</div>";
    } else {
        echo "<div class='warning'>⚠️ Missing $description: $dir (will be created automatically)</div>";
    }
}

// Test 3: Check PHP extensions
echo "<h2>🔧 PHP Extensions Check</h2>";

$requiredExtensions = [
    'gd' => 'GD Library (for image processing)',
    'fileinfo' => 'File Info (for MIME type detection)',
    'exif' => 'EXIF (for image metadata)'
];

foreach ($requiredExtensions as $ext => $description) {
    if (extension_loaded($ext)) {
        echo "<div class='success'>✅ $description: $ext</div>";
    } else {
        echo "<div class='error'>❌ Missing $description: $ext</div>";
    }
}

// Test 4: Check upload settings
echo "<h2>⚙️ PHP Upload Settings</h2>";

$uploadSettings = [
    'file_uploads' => ini_get('file_uploads') ? 'Enabled' : 'Disabled',
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'max_file_uploads' => ini_get('max_file_uploads'),
    'memory_limit' => ini_get('memory_limit')
];

foreach ($uploadSettings as $setting => $value) {
    $class = ($setting === 'file_uploads' && $value === 'Disabled') ? 'error' : 'info';
    echo "<div class='$class'>📋 $setting: $value</div>";
}

// Test 5: Test ImageUpload class
echo "<h2>🧪 ImageUpload Class Test</h2>";

try {
    require_once 'includes/image_upload.php';
    $imageUpload = new ImageUpload();
    echo "<div class='success'>✅ ImageUpload class loaded successfully</div>";
    
    // Test URL generation
    $testUrl = $imageUpload->getImageUrl('test.jpg');
    echo "<div class='info'>📋 Test URL generation: $testUrl</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error loading ImageUpload class: " . $e->getMessage() . "</div>";
}

// Test 6: Sample upload form
echo "<h2>📤 Test Upload Form</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_image'])) {
    try {
        require_once 'includes/image_upload.php';
        $imageUpload = new ImageUpload();
        
        $result = $imageUpload->uploadImage($_FILES['test_image'], 'TEST');
        
        if ($result['success']) {
            echo "<div class='success'>✅ Upload successful!</div>";
            echo "<div class='info'>📋 Filename: {$result['filename']}</div>";
            echo "<div class='info'>📋 Thumbnail: {$result['thumbnail']}</div>";
            echo "<div class='info'>📋 Original: {$result['original']}</div>";
            
            // Display uploaded images
            echo "<h3>Uploaded Images:</h3>";
            echo "<div style='display:flex;gap:20px;margin:20px 0;'>";
            echo "<div>";
            echo "<p><strong>Main Image:</strong></p>";
            echo "<img src='{$imageUpload->getImageUrl($result['filename'])}' style='max-width:200px;border:1px solid #ddd;'>";
            echo "</div>";
            echo "<div>";
            echo "<p><strong>Thumbnail:</strong></p>";
            echo "<img src='{$imageUpload->getImageUrl($result['filename'], 'thumbnail')}' style='max-width:150px;border:1px solid #ddd;'>";
            echo "</div>";
            echo "</div>";
            
        } else {
            echo "<div class='error'>❌ Upload failed: {$result['message']}</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Upload error: " . $e->getMessage() . "</div>";
    }
}

echo "<form method='POST' enctype='multipart/form-data' style='border:1px solid #ddd;padding:20px;border-radius:8px;'>";
echo "<h3>Test Image Upload</h3>";
echo "<p>Select an image file to test the upload functionality:</p>";
echo "<input type='file' name='test_image' accept='image/*' required style='margin:10px 0;'>";
echo "<br>";
echo "<button type='submit' style='background:#007bff;color:white;padding:10px 20px;border:none;border-radius:4px;cursor:pointer;'>Upload Test Image</button>";
echo "</form>";

// Test 7: Database integration check
echo "<h2>🗄️ Database Integration Check</h2>";

try {
    require_once 'config/db_connect.php';
    
    if (isset($db_connection_error)) {
        echo "<div class='error'>❌ Database connection failed!</div>";
    } else {
        echo "<div class='success'>✅ Database connected successfully</div>";
        
        // Check if image_url column exists in products table
        $stmt = $conn->query("DESCRIBE products");
        $columns = $stmt->fetchAll();
        $columnNames = array_column($columns, 'Field');
        
        if (in_array('image_url', $columnNames)) {
            echo "<div class='success'>✅ image_url column exists in products table</div>";
        } else {
            echo "<div class='error'>❌ image_url column missing in products table</div>";
        }
        
        // Check for products with images
        $stmt = $conn->query("SELECT COUNT(*) as total, COUNT(image_url) as with_images FROM products WHERE image_url IS NOT NULL AND image_url != ''");
        $stats = $stmt->fetch();
        echo "<div class='info'>📋 Products with images: {$stats['with_images']} out of {$stats['total']} total products</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
}

echo "<h2>🎯 Quick Actions</h2>";
echo "<div class='info'>";
echo "<p><strong>Test the complete system:</strong></p>";
echo "<a href='supplier/add_product.php' style='display:inline-block;padding:8px 16px;margin:4px;background:#28a745;color:white;text-decoration:none;border-radius:4px;'>Add Product with Image</a> ";
echo "<a href='supplier/products.php' style='display:inline-block;padding:8px 16px;margin:4px;background:#007bff;color:white;text-decoration:none;border-radius:4px;'>View Products</a> ";
echo "<a href='login.php' style='display:inline-block;padding:8px 16px;margin:4px;background:#ffc107;color:#212529;text-decoration:none;border-radius:4px;'>Login Page</a>";
echo "</div>";

echo "<div class='info'>";
echo "<p><strong>Test Credentials:</strong></p>";
echo "<ul>";
echo "<li><strong>Supplier:</strong> Username: <code>testsupplier</code>, Password: <code>password123</code></li>";
echo "</ul>";
echo "</div>";

echo "</body></html>";
?>
