<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login System - PiGit</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 20px auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .success { background: #e6ffe6; color: green; padding: 15px; border-radius: 6px; margin: 15px 0; }
        .error { background: #ffe6e6; color: red; padding: 15px; border-radius: 6px; margin: 15px 0; }
        .info { background: #e6f3ff; color: blue; padding: 15px; border-radius: 6px; margin: 15px 0; }
        button { background: #f97316; color: white; padding: 12px 24px; border: none; border-radius: 6px; cursor: pointer; margin: 5px; font-weight: bold; }
        button:hover { background: #ea580c; }
        button.secondary { background: #6b7280; }
        button.secondary:hover { background: #4b5563; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .card { background: #f9f9f9; padding: 20px; border-radius: 8px; border-left: 4px solid #f97316; }
        table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #f0f0f0; font-weight: bold; }
        .role-supplier { background: #dbeafe; }
        .role-retailer { background: #dcfce7; }
        .role-farmer { background: #fef3c7; }
        input, select { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #f97316;">Login System Test - PiGit</h1>
        
        <?php
        session_start();
        
        // Display session messages
        if (isset($_SESSION['success'])) {
            echo "<div class='success'>✅ " . htmlspecialchars($_SESSION['success']) . "</div>";
            unset($_SESSION['success']);
        }
        
        if (isset($_SESSION['login_errors'])) {
            echo "<div class='error'>❌ Login Errors:<ul>";
            foreach ($_SESSION['login_errors'] as $field => $error) {
                echo "<li><strong>$field:</strong> " . htmlspecialchars($error) . "</li>";
            }
            echo "</ul></div>";
            unset($_SESSION['login_errors']);
        }
        ?>
        
        <div class="info">
            <h3>🎯 Login System Testing</h3>
            <p>This page tests the complete login system for all user roles: <strong>Farmers</strong>, <strong>Suppliers</strong>, and <strong>Retailers</strong>.</p>
            <p><strong>Features:</strong></p>
            <ul>
                <li>Role-based authentication and redirection</li>
                <li>Session management with user data</li>
                <li>Remember me functionality</li>
                <li>Secure password verification</li>
                <li>Automatic dashboard redirection based on role</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>🚀 Quick Access</h2>
        <div class="grid">
            <div class="card">
                <h3>📝 Login Form</h3>
                <p>Open the main login page</p>
                <button onclick="window.open('login.php', '_blank')">Open Login Page</button>
            </div>
            
            <div class="card">
                <h3>📋 Registration</h3>
                <p>Register new users</p>
                <button onclick="window.open('login.php?action=register', '_blank')" class="secondary">Basic Registration</button>
                <button onclick="window.open('register.php', '_blank')" class="secondary">Business Registration</button>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>👥 Test User Accounts</h2>
        <p>Use these test accounts to verify login functionality:</p>
        
        <div class="grid">
            <!-- Test Supplier Login -->
            <div class="card">
                <h3>🚚 Test Supplier Login</h3>
                <form action="login.php" method="POST">
                    <p><strong>Test Credentials:</strong></p>
                    <input type="text" name="username" placeholder="Username" value="testsupplier" readonly>
                    <input type="password" name="password" placeholder="Password" value="password123" readonly>
                    <input type="hidden" name="login" value="1">
                    <button type="submit">Login as Supplier</button>
                </form>
                <p style="font-size: 12px; color: #666; margin-top: 10px;">
                    Should redirect to: <strong>supplier/dashboard.php</strong>
                </p>
            </div>
            
            <!-- Test Retailer Login -->
            <div class="card">
                <h3>🏪 Test Retailer Login</h3>
                <form action="login.php" method="POST">
                    <p><strong>Test Credentials:</strong></p>
                    <input type="text" name="username" placeholder="Username" value="testretailer" readonly>
                    <input type="password" name="password" placeholder="Password" value="password123" readonly>
                    <input type="hidden" name="login" value="1">
                    <button type="submit">Login as Retailer</button>
                </form>
                <p style="font-size: 12px; color: #666; margin-top: 10px;">
                    Should redirect to: <strong>dashboard/retailer_dashboard.php</strong>
                </p>
            </div>
            
            <!-- Test Farmer Login -->
            <div class="card">
                <h3>🌾 Test Farmer Login</h3>
                <form action="login.php" method="POST">
                    <p><strong>Test Credentials:</strong></p>
                    <input type="text" name="username" placeholder="Username" value="testfarmer" readonly>
                    <input type="password" name="password" placeholder="Password" value="password123" readonly>
                    <input type="hidden" name="login" value="1">
                    <button type="submit">Login as Farmer</button>
                </form>
                <p style="font-size: 12px; color: #666; margin-top: 10px;">
                    Should redirect to: <strong>dashboard/farmer_dashboard.php</strong>
                </p>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🔧 Manual Login Test</h2>
        <p>Test login with custom credentials:</p>
        
        <form action="login.php" method="POST" style="max-width: 400px;">
            <div style="margin-bottom: 15px;">
                <label><strong>Username or Email:</strong></label>
                <input type="text" name="username" placeholder="Enter username or email" required>
            </div>
            
            <div style="margin-bottom: 15px;">
                <label><strong>Password:</strong></label>
                <input type="password" name="password" placeholder="Enter password" required>
            </div>
            
            <div style="margin-bottom: 15px;">
                <label>
                    <input type="checkbox" name="remember" style="width: auto; margin-right: 10px;">
                    Remember me
                </label>
            </div>
            
            <input type="hidden" name="login" value="1">
            <button type="submit">Login</button>
        </form>
    </div>

    <div class="container">
        <h2>📊 Database Status</h2>
        <?php
        try {
            require_once 'config/db_connect.php';
            
            if (isset($db_connection_error)) {
                echo "<div class='error'>❌ Database connection failed!</div>";
            } else {
                echo "<div class='success'>✅ Database connected successfully</div>";
                
                // Show user statistics
                $stmt = $conn->query("SELECT role, COUNT(*) as count FROM users GROUP BY role");
                $stats = $stmt->fetchAll();
                
                echo "<h3>User Statistics</h3>";
                echo "<table>";
                echo "<tr><th>Role</th><th>Count</th></tr>";
                foreach ($stats as $stat) {
                    $roleClass = 'role-' . $stat['role'];
                    echo "<tr class='$roleClass'><td>" . ucfirst($stat['role']) . "</td><td>" . $stat['count'] . "</td></tr>";
                }
                echo "</table>";
                
                // Show recent users with login credentials
                $stmt = $conn->query("SELECT fullname, username, email, role, company_name, created_at FROM users ORDER BY created_at DESC LIMIT 10");
                $users = $stmt->fetchAll();
                
                if (count($users) > 0) {
                    echo "<h3>Available User Accounts</h3>";
                    echo "<table>";
                    echo "<tr><th>Name</th><th>Username</th><th>Email</th><th>Role</th><th>Company</th><th>Created</th><th>Test Login</th></tr>";
                    foreach ($users as $user) {
                        $roleClass = 'role-' . $user['role'];
                        echo "<tr class='$roleClass'>";
                        echo "<td>" . htmlspecialchars($user['fullname']) . "</td>";
                        echo "<td>" . htmlspecialchars($user['username']) . "</td>";
                        echo "<td>" . htmlspecialchars($user['email']) . "</td>";
                        echo "<td><strong>" . ucfirst($user['role']) . "</strong></td>";
                        echo "<td>" . htmlspecialchars($user['company_name'] ?? 'N/A') . "</td>";
                        echo "<td>" . $user['created_at'] . "</td>";
                        echo "<td>";
                        echo "<form action='login.php' method='POST' style='display: inline;'>";
                        echo "<input type='hidden' name='username' value='" . htmlspecialchars($user['username']) . "'>";
                        echo "<input type='hidden' name='password' value='password123'>";
                        echo "<input type='hidden' name='login' value='1'>";
                        echo "<button type='submit' style='padding: 5px 10px; font-size: 12px;'>Login</button>";
                        echo "</form>";
                        echo "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                    echo "<p style='font-size: 12px; color: #666;'><strong>Note:</strong> All test accounts use password: <code>password123</code></p>";
                } else {
                    echo "<div class='info'>No users found. <a href='test_supplier_retailer_registration.php'>Create some test users first</a>.</div>";
                }
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
        }
        ?>
    </div>

    <div class="container">
        <h2>🔍 Session Information</h2>
        <?php if (isset($_SESSION['user_id'])): ?>
            <div class="success">
                <h3>✅ User is logged in!</h3>
                <ul>
                    <li><strong>User ID:</strong> <?php echo $_SESSION['user_id']; ?></li>
                    <li><strong>Name:</strong> <?php echo htmlspecialchars($_SESSION['fullname']); ?></li>
                    <li><strong>Username:</strong> <?php echo htmlspecialchars($_SESSION['username']); ?></li>
                    <li><strong>Email:</strong> <?php echo htmlspecialchars($_SESSION['email']); ?></li>
                    <li><strong>Role:</strong> <?php echo ucfirst($_SESSION['role']); ?></li>
                    <?php if ($_SESSION['company_name']): ?>
                        <li><strong>Company:</strong> <?php echo htmlspecialchars($_SESSION['company_name']); ?></li>
                    <?php endif; ?>
                    <li><strong>Login Time:</strong> <?php echo date('Y-m-d H:i:s', $_SESSION['login_time']); ?></li>
                </ul>
                <p>
                    <a href="logout.php" style="color: red; font-weight: bold;">Logout</a> |
                    <?php
                    switch ($_SESSION['role']) {
                        case 'supplier':
                            echo "<a href='supplier/dashboard.php'>Go to Supplier Dashboard</a>";
                            break;
                        case 'retailer':
                            echo "<a href='dashboard/retailer_dashboard.php'>Go to Retailer Dashboard</a>";
                            break;
                        case 'farmer':
                            echo "<a href='dashboard/farmer_dashboard.php'>Go to Farmer Dashboard</a>";
                            break;
                        default:
                            echo "<a href='dashboard/dashboard.php'>Go to Dashboard</a>";
                    }
                    ?>
                </p>
            </div>
        <?php else: ?>
            <div class="info">
                <h3>ℹ️ No user logged in</h3>
                <p>Use the login forms above to test the authentication system.</p>
            </div>
        <?php endif; ?>
        
        <h3>Current Session Data:</h3>
        <pre style="background: #f0f0f0; padding: 15px; border-radius: 6px; overflow: auto;"><?php print_r($_SESSION); ?></pre>
    </div>

    <div class="container">
        <h2>🔗 Related Tools</h2>
        <div class="grid">
            <div class="card">
                <h3>Registration Tools</h3>
                <button onclick="window.open('test_supplier_retailer_registration.php', '_blank')" class="secondary">Registration Test Suite</button>
                <button onclick="window.open('setup_supplier_retailer_db.php', '_blank')" class="secondary">Database Setup</button>
            </div>
            
            <div class="card">
                <h3>Debug Tools</h3>
                <button onclick="window.open('debug_registration.php', '_blank')" class="secondary">Debug Registration</button>
                <button onclick="location.reload()">Refresh This Page</button>
            </div>
        </div>
    </div>

</body>
</html>
