<?php
// Comprehensive test for product management functionality
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html><head><title>Test Product Management</title>";
echo "<style>body{font-family:Arial;max-width:1000px;margin:20px auto;padding:20px;} .success{background:#e6ffe6;color:green;padding:10px;border-radius:4px;margin:10px 0;} .error{background:#ffe6e6;color:red;padding:10px;border-radius:4px;margin:10px 0;} .info{background:#e6f3ff;color:blue;padding:10px;border-radius:4px;margin:10px 0;} .warning{background:#fff3cd;color:#856404;padding:10px;border-radius:4px;margin:10px 0;} table{width:100%;border-collapse:collapse;margin:20px 0;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background:#f5f5f5;} .btn{display:inline-block;padding:8px 16px;margin:4px;text-decoration:none;border-radius:4px;color:white;} .btn-primary{background:#007bff;} .btn-success{background:#28a745;} .btn-danger{background:#dc3545;} .btn-warning{background:#ffc107;color:#212529;}</style>";
echo "</head><body>";

echo "<h1>🧪 Product Management Test Suite</h1>";

try {
    require_once 'config/db_connect.php';
    
    if (isset($db_connection_error)) {
        echo "<div class='error'>❌ Database connection failed!</div>";
        exit;
    }
    
    echo "<div class='success'>✅ Database connected successfully!</div>";
    
    // Test 1: Check if all required files exist
    echo "<h2>📁 File Existence Check</h2>";
    
    $requiredFiles = [
        'supplier/products.php' => 'Products listing page',
        'supplier/add_product.php' => 'Add product form',
        'supplier/edit_product.php' => 'Edit product form',
        'supplier/delete_product.php' => 'Delete product script',
        'config/db_connect.php' => 'Database connection',
        'setup_supplier_retailer_db.php' => 'Database setup script'
    ];
    
    foreach ($requiredFiles as $file => $description) {
        if (file_exists($file)) {
            echo "<div class='success'>✅ $description: $file</div>";
        } else {
            echo "<div class='error'>❌ Missing $description: $file</div>";
        }
    }
    
    // Test 2: Database structure check
    echo "<h2>🗄️ Database Structure Check</h2>";
    
    $requiredTables = ['users', 'products', 'orders', 'order_items'];
    foreach ($requiredTables as $table) {
        try {
            $stmt = $conn->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<div class='success'>✅ Table '$table' exists with $count records</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Table '$table' missing or error: " . $e->getMessage() . "</div>";
        }
    }
    
    // Test 3: Check products table structure
    echo "<h2>📦 Products Table Structure</h2>";
    try {
        $stmt = $conn->query("DESCRIBE products");
        $columns = $stmt->fetchAll();
        
        $requiredColumns = ['id', 'supplier_id', 'name', 'sku', 'category', 'price', 'stock_quantity', 'min_stock_level'];
        $existingColumns = array_column($columns, 'Field');
        
        foreach ($requiredColumns as $column) {
            if (in_array($column, $existingColumns)) {
                echo "<div class='success'>✅ Column '$column' exists</div>";
            } else {
                echo "<div class='error'>❌ Missing column '$column'</div>";
            }
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error checking products table: " . $e->getMessage() . "</div>";
    }
    
    // Test 4: Check for suppliers
    echo "<h2>👥 Supplier Users Check</h2>";
    try {
        $stmt = $conn->query("SELECT id, fullname, username, company_name FROM users WHERE role = 'supplier'");
        $suppliers = $stmt->fetchAll();
        
        if (empty($suppliers)) {
            echo "<div class='warning'>⚠️ No suppliers found! You need to create a supplier user first.</div>";
            echo "<p><a href='create_test_users.php' class='btn btn-primary'>Create Test Users</a></p>";
        } else {
            echo "<div class='success'>✅ Found " . count($suppliers) . " supplier(s)</div>";
            echo "<table>";
            echo "<tr><th>ID</th><th>Full Name</th><th>Username</th><th>Company</th><th>Actions</th></tr>";
            foreach ($suppliers as $supplier) {
                echo "<tr>";
                echo "<td>{$supplier['id']}</td>";
                echo "<td>{$supplier['fullname']}</td>";
                echo "<td>{$supplier['username']}</td>";
                echo "<td>{$supplier['company_name']}</td>";
                echo "<td><a href='#' onclick='testSupplierLogin(\"{$supplier['username']}\")' class='btn btn-primary'>Test Login</a></td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error checking suppliers: " . $e->getMessage() . "</div>";
    }
    
    // Test 5: Check products
    echo "<h2>📦 Products Check</h2>";
    try {
        $stmt = $conn->query("
            SELECT p.*, u.fullname as supplier_name, u.company_name 
            FROM products p 
            JOIN users u ON p.supplier_id = u.id 
            ORDER BY u.fullname, p.name
        ");
        $products = $stmt->fetchAll();
        
        if (empty($products)) {
            echo "<div class='warning'>⚠️ No products found. Run the database setup to add sample products.</div>";
            echo "<p><a href='setup_supplier_retailer_db.php' class='btn btn-warning'>Setup Database</a></p>";
        } else {
            echo "<div class='success'>✅ Found " . count($products) . " product(s)</div>";
            echo "<table>";
            echo "<tr><th>ID</th><th>Product Name</th><th>SKU</th><th>Category</th><th>Price</th><th>Stock</th><th>Status</th><th>Supplier</th><th>Actions</th></tr>";
            foreach ($products as $product) {
                $stockStatus = '';
                if ($product['stock_quantity'] <= 0) {
                    $stockStatus = '<span style="color:red;">Out of Stock</span>';
                } elseif ($product['stock_quantity'] <= $product['min_stock_level']) {
                    $stockStatus = '<span style="color:orange;">Low Stock</span>';
                } else {
                    $stockStatus = '<span style="color:green;">In Stock</span>';
                }
                
                echo "<tr>";
                echo "<td>{$product['id']}</td>";
                echo "<td>{$product['name']}</td>";
                echo "<td>{$product['sku']}</td>";
                echo "<td>{$product['category']}</td>";
                echo "<td>\${$product['price']}</td>";
                echo "<td>{$product['stock_quantity']} / {$product['min_stock_level']}</td>";
                echo "<td>$stockStatus</td>";
                echo "<td>{$product['supplier_name']}</td>";
                echo "<td>";
                echo "<a href='supplier/edit_product.php?id={$product['id']}' class='btn btn-primary'>Edit</a> ";
                echo "<a href='supplier/delete_product.php?id={$product['id']}' class='btn btn-danger' onclick='return confirm(\"Delete {$product['name']}?\")'>Delete</a>";
                echo "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error checking products: " . $e->getMessage() . "</div>";
    }
    
    // Test 6: Test URLs
    echo "<h2>🔗 URL Testing</h2>";
    
    $testUrls = [
        'supplier/products.php' => 'Products Page',
        'supplier/add_product.php' => 'Add Product Form',
        'login.php' => 'Login Page',
        'register.php' => 'Registration Page'
    ];
    
    foreach ($testUrls as $url => $description) {
        echo "<div class='info'>";
        echo "🔗 <strong>$description:</strong> ";
        echo "<a href='$url' target='_blank' class='btn btn-primary'>Open $url</a>";
        echo "</div>";
    }
    
    // Test 7: Product Management Statistics
    echo "<h2>📊 Product Statistics</h2>";
    try {
        $stmt = $conn->query("
            SELECT 
                COUNT(*) as total_products,
                SUM(CASE WHEN stock_quantity > min_stock_level THEN 1 ELSE 0 END) as in_stock,
                SUM(CASE WHEN stock_quantity <= min_stock_level AND stock_quantity > 0 THEN 1 ELSE 0 END) as low_stock,
                SUM(CASE WHEN stock_quantity <= 0 THEN 1 ELSE 0 END) as out_of_stock,
                SUM(price * stock_quantity) as total_inventory_value,
                COUNT(DISTINCT supplier_id) as active_suppliers
            FROM products
        ");
        $stats = $stmt->fetch();
        
        echo "<div class='info'>";
        echo "<strong>Overall Statistics:</strong><br>";
        echo "📦 Total Products: {$stats['total_products']}<br>";
        echo "✅ In Stock: {$stats['in_stock']}<br>";
        echo "⚠️ Low Stock: {$stats['low_stock']}<br>";
        echo "❌ Out of Stock: {$stats['out_of_stock']}<br>";
        echo "💰 Total Inventory Value: $" . number_format($stats['total_inventory_value'], 2) . "<br>";
        echo "👥 Active Suppliers: {$stats['active_suppliers']}<br>";
        echo "</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error calculating statistics: " . $e->getMessage() . "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Test failed: " . $e->getMessage() . "</div>";
}

echo "<h2>🎯 Quick Actions</h2>";
echo "<div class='info'>";
echo "<p><strong>Setup and Test:</strong></p>";
echo "<a href='setup_supplier_retailer_db.php' class='btn btn-warning'>Setup Database & Sample Data</a> ";
echo "<a href='create_test_users.php' class='btn btn-success'>Create Test Users</a> ";
echo "<a href='login.php' class='btn btn-primary'>Login Page</a> ";
echo "<a href='supplier/products.php' class='btn btn-primary'>Products Management</a>";
echo "</div>";

echo "<div class='info'>";
echo "<p><strong>Test Credentials:</strong></p>";
echo "<ul>";
echo "<li><strong>Supplier:</strong> Username: <code>testsupplier</code>, Password: <code>password123</code></li>";
echo "<li><strong>Retailer:</strong> Username: <code>testretailer</code>, Password: <code>password123</code></li>";
echo "</ul>";
echo "</div>";

echo "<script>";
echo "function testSupplierLogin(username) {";
echo "  alert('To test login as ' + username + ':\\n\\n1. Click Login Page\\n2. Use username: ' + username + '\\n3. Use password: password123\\n4. Navigate to Products page');";
echo "}";
echo "</script>";

echo "</body></html>";
?>
