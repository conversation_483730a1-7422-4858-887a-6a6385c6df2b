<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Register.php - Supplier & Retailer</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 20px auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .success { background: #e6ffe6; color: green; padding: 15px; border-radius: 6px; margin: 15px 0; }
        .error { background: #ffe6e6; color: red; padding: 15px; border-radius: 6px; margin: 15px 0; }
        .info { background: #e6f3ff; color: blue; padding: 15px; border-radius: 6px; margin: 15px 0; }
        button { background: #f97316; color: white; padding: 12px 24px; border: none; border-radius: 6px; cursor: pointer; margin: 5px; font-weight: bold; }
        button:hover { background: #ea580c; }
        button.secondary { background: #6b7280; }
        button.secondary:hover { background: #4b5563; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .card { background: #f9f9f9; padding: 20px; border-radius: 8px; border-left: 4px solid #f97316; }
        table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #f0f0f0; font-weight: bold; }
        .role-supplier { background: #dbeafe; }
        .role-retailer { background: #dcfce7; }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #f97316;">Test Register.php - Supplier & Retailer Registration</h1>
        
        <?php
        session_start();
        
        // Display session messages
        if (isset($_SESSION['success'])) {
            echo "<div class='success'>✅ " . htmlspecialchars($_SESSION['success']) . "</div>";
            unset($_SESSION['success']);
        }
        
        if (isset($_SESSION['reg_errors'])) {
            echo "<div class='error'>❌ Registration Errors:<ul>";
            foreach ($_SESSION['reg_errors'] as $field => $error) {
                echo "<li><strong>$field:</strong> " . htmlspecialchars($error) . "</li>";
            }
            echo "</ul></div>";
            unset($_SESSION['reg_errors']);
        }
        ?>
        
        <div class="info">
            <h3>🎯 About register.php</h3>
            <p>This page tests the main <strong>register.php</strong> file which provides a comprehensive registration form specifically for <strong>Suppliers</strong> and <strong>Retailers</strong>.</p>
            <p><strong>Features:</strong></p>
            <ul>
                <li>Visual role selection between Supplier and Retailer</li>
                <li>Complete business information collection</li>
                <li>Professional UI with Tailwind CSS</li>
                <li>Comprehensive validation</li>
                <li>Database integration with business-specific fields</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>🚀 Quick Access</h2>
        <div class="grid">
            <div class="card">
                <h3>📝 Registration Form</h3>
                <p>Open the main registration form for suppliers and retailers</p>
                <button onclick="window.open('register.php', '_blank')">Open register.php</button>
            </div>
            
            <div class="card">
                <h3>🔧 Setup & Debug</h3>
                <p>Ensure database is properly configured</p>
                <button onclick="window.open('setup_supplier_retailer_db.php', '_blank')" class="secondary">Setup Database</button>
                <button onclick="location.reload()">Refresh This Page</button>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>⚡ Quick Registration Tests</h2>
        <p>Test registration with predefined data:</p>
        
        <div class="grid">
            <!-- Test Supplier Registration -->
            <div class="card">
                <h3>🚚 Test Supplier Registration</h3>
                <form action="register.php" method="POST" style="display: inline;">
                    <input type="hidden" name="fullName" value="Test Supplier User <?php echo time(); ?>">
                    <input type="hidden" name="email" value="supplier<?php echo time(); ?>@test.com">
                    <input type="hidden" name="username" value="supplier<?php echo time(); ?>">
                    <input type="hidden" name="password" value="password123">
                    <input type="hidden" name="confirmPassword" value="password123">
                    <input type="hidden" name="role" value="supplier">
                    <input type="hidden" name="companyName" value="ABC Feed Supplies Inc.">
                    <input type="hidden" name="businessType" value="feed_supplier">
                    <input type="hidden" name="phone" value="******-123-4567">
                    <input type="hidden" name="address" value="123 Supply Street, Business District, New York, NY 10001">
                    <input type="hidden" name="terms" value="1">
                    <button type="submit">Register Test Supplier</button>
                </form>
                <p style="font-size: 12px; color: #666; margin-top: 10px;">
                    Company: ABC Feed Supplies Inc.<br>
                    Type: Feed Supplier<br>
                    Email: supplier[timestamp]@test.com
                </p>
            </div>
            
            <!-- Test Retailer Registration -->
            <div class="card">
                <h3>🏪 Test Retailer Registration</h3>
                <form action="register.php" method="POST" style="display: inline;">
                    <input type="hidden" name="fullName" value="Test Retailer User <?php echo time(); ?>">
                    <input type="hidden" name="email" value="retailer<?php echo time(); ?>@test.com">
                    <input type="hidden" name="username" value="retailer<?php echo time(); ?>">
                    <input type="hidden" name="password" value="password123">
                    <input type="hidden" name="confirmPassword" value="password123">
                    <input type="hidden" name="role" value="retailer">
                    <input type="hidden" name="companyName" value="XYZ Grocery Chain">
                    <input type="hidden" name="businessType" value="grocery_retailer">
                    <input type="hidden" name="phone" value="******-987-6543">
                    <input type="hidden" name="address" value="456 Retail Avenue, Shopping Center, Los Angeles, CA 90210">
                    <input type="hidden" name="terms" value="1">
                    <button type="submit">Register Test Retailer</button>
                </form>
                <p style="font-size: 12px; color: #666; margin-top: 10px;">
                    Company: XYZ Grocery Chain<br>
                    Type: Grocery Retailer<br>
                    Email: retailer[timestamp]@test.com
                </p>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📊 Database Status</h2>
        <?php
        try {
            require_once 'config/db_connect.php';
            
            if (isset($db_connection_error)) {
                echo "<div class='error'>❌ Database connection failed! Please check your database configuration.</div>";
            } else {
                echo "<div class='success'>✅ Database connected successfully</div>";
                
                // Check table structure
                $stmt = $conn->query("DESCRIBE users");
                $columns = $stmt->fetchAll();
                $columnNames = array_column($columns, 'Field');
                
                echo "<div class='info'>";
                echo "<strong>Users table columns:</strong> " . implode(', ', $columnNames);
                echo "</div>";
                
                // Check if required columns exist
                $requiredColumns = ['role', 'company_name', 'business_type', 'phone', 'address'];
                $missingColumns = array_diff($requiredColumns, $columnNames);
                
                if (!empty($missingColumns)) {
                    echo "<div class='error'>❌ Missing columns: " . implode(', ', $missingColumns) . "</div>";
                    echo "<p><button onclick=\"window.open('setup_supplier_retailer_db.php', '_blank')\" class='secondary'>Run Database Setup</button></p>";
                } else {
                    echo "<div class='success'>✅ All required columns exist</div>";
                }
                
                // Show user statistics
                $stmt = $conn->query("SELECT role, COUNT(*) as count FROM users GROUP BY role");
                $stats = $stmt->fetchAll();
                
                echo "<h3>User Statistics</h3>";
                echo "<table>";
                echo "<tr><th>Role</th><th>Count</th></tr>";
                foreach ($stats as $stat) {
                    $roleClass = 'role-' . $stat['role'];
                    echo "<tr class='$roleClass'><td>" . ucfirst($stat['role']) . "</td><td>" . $stat['count'] . "</td></tr>";
                }
                echo "</table>";
                
                // Show recent supplier and retailer registrations
                $stmt = $conn->query("SELECT fullname, username, email, role, company_name, business_type, created_at FROM users WHERE role IN ('supplier', 'retailer') ORDER BY created_at DESC LIMIT 10");
                $users = $stmt->fetchAll();
                
                if (count($users) > 0) {
                    echo "<h3>Recent Supplier & Retailer Registrations</h3>";
                    echo "<table>";
                    echo "<tr><th>Name</th><th>Username</th><th>Email</th><th>Role</th><th>Company</th><th>Business Type</th><th>Created</th></tr>";
                    foreach ($users as $user) {
                        $roleClass = 'role-' . $user['role'];
                        echo "<tr class='$roleClass'>";
                        echo "<td>" . htmlspecialchars($user['fullname']) . "</td>";
                        echo "<td>" . htmlspecialchars($user['username']) . "</td>";
                        echo "<td>" . htmlspecialchars($user['email']) . "</td>";
                        echo "<td><strong>" . ucfirst($user['role']) . "</strong></td>";
                        echo "<td>" . htmlspecialchars($user['company_name'] ?? 'N/A') . "</td>";
                        echo "<td>" . htmlspecialchars($user['business_type'] ?? 'N/A') . "</td>";
                        echo "<td>" . $user['created_at'] . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                } else {
                    echo "<div class='info'>No supplier or retailer registrations found yet. Use the test buttons above to create some!</div>";
                }
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
        }
        ?>
    </div>

    <div class="container">
        <h2>🔍 Code Preview</h2>
        <div class="info">
            <h3>Key Features of register.php:</h3>
            <pre style="background: #f0f0f0; padding: 15px; border-radius: 6px; overflow: auto;">
// Role Selection with Visual Cards
&lt;div class="role-card" onclick="selectRole('supplier')"&gt;
    &lt;h3&gt;Supplier&lt;/h3&gt;
    &lt;p&gt;Supply feed, equipment, and farming materials&lt;/p&gt;
    &lt;input type="radio" name="role" value="supplier"&gt;
&lt;/div&gt;

&lt;div class="role-card" onclick="selectRole('retailer')"&gt;
    &lt;h3&gt;Retailer&lt;/h3&gt;
    &lt;p&gt;Sell farm products to consumers&lt;/p&gt;
    &lt;input type="radio" name="role" value="retailer"&gt;
&lt;/div&gt;

// Business Information Fields
&lt;input type="text" name="companyName" placeholder="Company Name" required&gt;
&lt;select name="businessType" required&gt;
    &lt;optgroup label="Supplier Types"&gt;
        &lt;option value="feed_supplier"&gt;Feed Supplier&lt;/option&gt;
        &lt;option value="equipment_supplier"&gt;Equipment Supplier&lt;/option&gt;
    &lt;/optgroup&gt;
    &lt;optgroup label="Retailer Types"&gt;
        &lt;option value="grocery_retailer"&gt;Grocery Retailer&lt;/option&gt;
        &lt;option value="restaurant_chain"&gt;Restaurant Chain&lt;/option&gt;
    &lt;/optgroup&gt;
&lt;/select&gt;
&lt;input type="tel" name="phone" placeholder="Phone Number" required&gt;
&lt;textarea name="address" placeholder="Business Address" required&gt;&lt;/textarea&gt;

// PHP Validation
if (!in_array($role, ['supplier', 'retailer'])) {
    $errors['role'] = "Please select either Supplier or Retailer";
}
if (empty($company_name)) {
    $errors['company_name'] = "Company name is required";
}

// Database Insertion
$sql = "INSERT INTO users (fullname, username, email, password, role, 
         company_name, business_type, phone, address) 
         VALUES (:fullname, :username, :email, :password, :role, 
         :company_name, :business_type, :phone, :address)";
            </pre>
        </div>
    </div>

    <div class="container">
        <h2>🔗 Related Files</h2>
        <div class="grid">
            <div class="card">
                <h3>Other Registration Options</h3>
                <button onclick="window.open('login.php?action=register', '_blank')" class="secondary">Main Login/Register</button>
                <button onclick="window.open('simple_supplier_retailer_register.php', '_blank')" class="secondary">Simple S&R Form</button>
            </div>
            
            <div class="card">
                <h3>Testing Tools</h3>
                <button onclick="window.open('test_supplier_retailer_registration.php', '_blank')" class="secondary">Full Test Suite</button>
                <button onclick="window.open('debug_registration.php', '_blank')" class="secondary">Debug Tools</button>
            </div>
        </div>
    </div>

</body>
</html>
